#!/usr/bin/env python3
"""
Test script for the Enhanced Reconciliation System
Creates sample data files to test the reconciliation functionality
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os

def create_sample_trader_file():
    """Create a sample trader file"""
    np.random.seed(42)
    
    # Sample data for Star AGL013 Trader
    data = {
        'Trade Date': [
            '2024-04-24', '2024-04-24', '2024-04-25', '2024-04-25', '2024-04-26'
        ],
        'UCC Code': ['AGL013001', 'AGL013002', 'AGL013003', 'AGL013001', 'AGL013002'],
        'State': ['MH', 'MH', 'DL', 'MH', 'DL'],
        'Symbol': ['NIFTY', 'BANKNIFTY', 'NIFTY', 'SENSEX', 'BANKNIFTY'],
        'Buy/Sell': ['BUY', 'SELL', 'BUY', 'SELL', 'BUY'],
        'Qty': [50, 25, 75, 100, 30],
        'Price': [22150.50, 48200.25, 22180.75, 72500.00, 48150.80],
        'Traded Value': [1107525.00, 1205006.25, 1663556.25, 7250000.00, 1444524.00],
        'Exchange': ['NSE', 'NSE', 'NSE', 'BSE', 'NSE'],
        'Client Name': ['Trader A', 'Trader B', 'Trader C', 'Trader A', 'Trader B'],
        'Order No.': ['ORD001', 'ORD002', 'ORD003', 'ORD004', 'ORD005'],
        'Lot': [1, 1, 1, 1, 1],
        'Contract': [50, 25, 75, 100, 30],
        'Multiplication Factor': [1, 1, 1, 1, 1],
        'Commission': [100.50, 75.25, 125.75, 200.00, 90.80]
    }
    
    df = pd.DataFrame(data)
    df.to_csv('Sample_Star_AGL013_Trade_File.csv', index=False)
    print("✅ Created Sample_Star_AGL013_Trade_File.csv")

def create_sample_broker_file():
    """Create a sample broker file (CQG format)"""
    np.random.seed(43)
    
    # Sample data for CQG Trader
    data = {
        'Date': ['24-Apr-2024', '24-Apr-2024', '25-Apr-2024', '25-Apr-2024', '26-Apr-2024'],
        'Account': ['CQG001', 'CQG002', 'CQG001', 'CQG003', 'CQG002'],
        'Symbol': ['NIFTY_FUT', 'BANK_NIFTY', 'NIFTY_FUT', 'SENSEX_FUT', 'BANK_NIFTY'],
        'Side': ['Buy', 'Sell', 'Buy', 'Sell', 'Buy'],
        'Quantity': [25, 50, 30, 40, 20],
        'Price': [22160.00, 48180.50, 22190.25, 72480.75, 48170.00],
        'Value': [554000.00, 2409025.00, 665707.50, 2899230.00, 963400.00],
        'Trader': ['John Doe', 'Jane Smith', 'John Doe', 'Bob Wilson', 'Jane Smith'],
        'Order ID': ['CQG_001', 'CQG_002', 'CQG_003', 'CQG_004', 'CQG_005'],
        'Commission': [50.00, 100.00, 60.00, 80.00, 45.00],
        'Exchange': ['NSE', 'NSE', 'NSE', 'BSE', 'NSE']
    }
    
    df = pd.DataFrame(data)
    df.to_csv('Sample_CQG_Trader_File.csv', index=False)
    print("✅ Created Sample_CQG_Trader_File.csv")

def create_sample_broker_file_2():
    """Create another sample broker file with different format"""
    np.random.seed(44)
    
    # Sample data for Star07 format
    data = {
        'Transaction Date': ['2024/04/24', '2024/04/24', '2024/04/25', '2024/04/26'],
        'Client Code': ['ST07001', 'ST07002', 'ST07001', 'ST07003'],
        'State Code': ['Maharashtra', 'Delhi', 'Maharashtra', 'Gujarat'],
        'Instrument': ['NIFTY50', 'BANKNIFTY', 'NIFTY50', 'FINNIFTY'],
        'Transaction Type': ['B', 'S', 'B', 'S'],
        'Volume': [40, 60, 35, 25],
        'Rate': [22140.25, 48220.75, 22175.50, 19850.00],
        'Total Value': [885610.00, 2893245.00, 776142.50, 496250.00],
        'Market': ['NSE', 'NSE', 'NSE', 'NSE'],
        'Trader ID': ['TR_A', 'TR_B', 'TR_A', 'TR_C'],
        'Reference': ['REF001', 'REF002', 'REF003', 'REF004'],
        'Lot Size': [50, 25, 50, 40],
        'Multiplier': [1, 1, 1, 1],
        'Brokerage': [88.56, 144.66, 77.61, 49.63]
    }
    
    df = pd.DataFrame(data)
    df.to_csv('Sample_Star07_Trade_File.csv', index=False)
    print("✅ Created Sample_Star07_Trade_File.csv")

def create_sample_files():
    """Create all sample files"""
    print("🔧 Creating sample test files...")
    print("-" * 40)
    
    create_sample_trader_file()
    create_sample_broker_file()
    create_sample_broker_file_2()
    
    print("-" * 40)
    print("✅ All sample files created successfully!")
    print("\nSample files created:")
    print("1. Sample_Star_AGL013_Trade_File.csv")
    print("2. Sample_CQG_Trader_File.csv") 
    print("3. Sample_Star07_Trade_File.csv")
    print("\nYou can now run the enhanced_reconciliation_system.py to test with these files.")

if __name__ == "__main__":
    create_sample_files()
