#!/usr/bin/env python3
"""
Test script for Dynamic Reconciliation System
Creates sample trader and broker files to demonstrate the system
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import random
import os

def create_trader_files():
    """Create multiple trader files with different formats"""
    
    # Trader File 1 - CQG Format
    trader1_data = {
        'Date': ['2024-04-24', '2024-04-24', '2024-04-25', '2024-04-25', '2024-04-26'],
        'Account': ['TRD001234', 'TRD001235', 'TRD001236', 'TRD001237', 'TRD001238'],
        'State': ['MH', 'gj', 'Karnataka', 'delhi', 'maha'],
        'Symbol': ['NIFTY24APR19000CE', 'BANKNIFTY24APR45000PE', 'NIFTY24MAY19500CE', 'SENSEX24APR70000CE', 'NIFTY24JUN20000PE'],
        'Side': ['Buy', 'Sell', 'Buy', 'Sell', 'Buy'],
        'Quantity': [50, 75, 100, 25, 150],
        'Price': [125.50, 89.75, 156.25, 234.80, 98.40],
        'Value': [6275.00, 6731.25, 15625.00, 5870.00, 14760.00],
        'Trader': ['Suraj B', 'Amit K', 'Priya S', 'Rahul M', 'Neha P'],
        'Order ID': ['ORD001', 'ORD002', 'ORD003', 'ORD004', 'ORD005'],
        'Commission': [15.50, 18.75, 25.00, 12.25, 22.50],
        'Contract': [1, 1, 1, 1, 1],
        'Lot': [50, 75, 100, 25, 150],
        'Mult Factor': [1, 1, 1, 1, 1],
        'Venue': ['NSE', 'NSE', 'NSE', 'BSE', 'NSE'],
        'Closing Price': [130.25, 85.50, 162.75, 240.20, 102.80]
    }
    
    df1 = pd.DataFrame(trader1_data)
    df1.to_csv('Trader_CQG_File_1.csv', index=False)
    print("✅ Created Trader_CQG_File_1.csv")
    
    # Trader File 2 - Different Format
    trader2_data = {
        'Trade Date': ['24/04/2024', '24/04/2024', '25/04/2024', '25/04/2024'],
        'UCC Code': ['*********', '*********', '*********', '*********'],
        'Client State': ['Maharashtra', 'Gujarat', 'kar', 'UP'],
        'Instrument': ['NIFTY24APR19200CE', 'BANKNIFTY24APR44500PE', 'NIFTY24MAY19800CE', 'SENSEX24APR69500CE'],
        'Buy/Sell': ['B', 'S', 'B', 'S'],
        'Qty': [25, 50, 75, 100],
        'Rate': [142.30, 95.60, 178.90, 201.45],
        'Traded Value': [3557.50, 4780.00, 13417.50, 20145.00],
        'Client Name': ['Investor A', 'Investor B', 'Investor C', 'Investor D'],
        'Order No.': ['TRD101', 'TRD102', 'TRD103', 'TRD104'],
        'Brokerage': [8.90, 11.95, 33.54, 50.36],
        'Contract Size': [1, 1, 1, 1],
        'Lot Size': [25, 50, 75, 100],
        'Multiplier': [1, 1, 1, 1],
        'Exchange': ['NSE', 'NSE', 'NSE', 'BSE'],
        'Close Rate': [145.80, 92.30, 185.20, 195.75]
    }
    
    df2 = pd.DataFrame(trader2_data)
    df2.to_csv('Trader_Star_File_2.csv', index=False)
    print("✅ Created Trader_Star_File_2.csv")
    
    # Trader File 3 - Another Format
    trader3_data = {
        'Transaction Date': ['2024-04-26', '2024-04-26', '2024-04-27'],
        'Account Number': ['ACC123456', 'ACC234567', 'ACC345678'],
        'Location': ['mh', 'Delhi', 'guj'],
        'Contract Symbol': ['NIFTY24APR20000CE', 'BANKNIFTY24APR46000PE', 'NIFTY24MAY20500CE'],
        'Transaction Type': ['Buy', 'Sell', 'Buy'],
        'Volume': [200, 150, 100],
        'Execution Price': [78.90, 112.45, 203.60],
        'Total Value': [15780.00, 16867.50, 20360.00],
        'Trader ID': ['T001', 'T002', 'T003'],
        'Reference': ['REF001', 'REF002', 'REF003'],
        'Fees': [39.45, 42.17, 50.90],
        'Contract Multiplier': [1, 1, 1],
        'Lot Multiplier': [200, 150, 100],
        'Factor': [1, 1, 1],
        'Market': ['NSE', 'NSE', 'NSE'],
        'Settlement Price': [82.15, 108.90, 210.30]
    }
    
    df3 = pd.DataFrame(trader3_data)
    df3.to_csv('Trader_Custom_File_3.csv', index=False)
    print("✅ Created Trader_Custom_File_3.csv")

def create_broker_files():
    """Create multiple broker files with different formats"""
    
    # Broker File 1
    broker1_data = {
        'Date': ['2024-04-24', '2024-04-24', '2024-04-25'],
        'Client Account': ['BRK001234567', 'BRK002345678', 'BRK003456789'],
        'State': ['Maharashtra', 'guj', 'KA'],
        'Symbol': ['NIFTY24APR19000CE', 'BANKNIFTY24APR45000PE', 'NIFTY24MAY19500CE'],
        'Side': ['Buy', 'Sell', 'Buy'],
        'Quantity': [100, 200, 150],
        'Price': [125.50, 89.75, 156.25],
        'Notional': [12550.00, 17950.00, 23437.50],
        'Broker Code': ['BRK001', 'BRK002', 'BRK003'],
        'Trade ID': ['BT001', 'BT002', 'BT003'],
        'Commission': [31.38, 44.88, 58.59],
        'Contract': [1, 1, 1],
        'Lot': [100, 200, 150],
        'Multiplication Factor': [1, 1, 1],
        'Venue': ['NSE', 'NSE', 'NSE'],
        'Closing Price': [130.25, 85.50, 162.75]
    }
    
    df1 = pd.DataFrame(broker1_data)
    df1.to_csv('Broker_Primary_File_1.csv', index=False)
    print("✅ Created Broker_Primary_File_1.csv")
    
    # Broker File 2 - Different Format
    broker2_data = {
        'Trading Date': ['26/04/2024', '26/04/2024', '27/04/2024'],
        'Account ID': ['987654', '876543', '765432'],
        'Region': ['MH', 'delhi', 'gujarat'],
        'Instrument Name': ['SENSEX24APR70000CE', 'NIFTY24JUN20000PE', 'BANKNIFTY24MAY47000CE'],
        'Direction': ['Long', 'Short', 'Long'],
        'Size': [50, 75, 125],
        'Fill Price': [234.80, 98.40, 187.65],
        'Fill Value': [11740.00, 7380.00, 23456.25],
        'Broker': ['Broker A', 'Broker B', 'Broker C'],
        'Order Reference': ['BR001', 'BR002', 'BR003'],
        'Brokerage': [29.35, 18.45, 58.64],
        'Contract Size': [1, 1, 1],
        'Lot Size': [50, 75, 125],
        'Multiplier': [1, 1, 1],
        'Exchange': ['BSE', 'NSE', 'NSE'],
        'Close Price': [240.20, 102.80, 192.30]
    }
    
    df2 = pd.DataFrame(broker2_data)
    df2.to_csv('Broker_Secondary_File_2.csv', index=False)
    print("✅ Created Broker_Secondary_File_2.csv")

def create_sample_files():
    """Create all sample files for testing"""
    print("🔧 Creating sample files for Dynamic Reconciliation System...")
    print("-" * 60)
    
    create_trader_files()
    print()
    create_broker_files()
    
    print("-" * 60)
    print("✅ All sample files created successfully!")
    print("\nTrader Files:")
    print("1. Trader_CQG_File_1.csv (5 records)")
    print("2. Trader_Star_File_2.csv (4 records)")
    print("3. Trader_Custom_File_3.csv (3 records)")
    print("\nBroker Files:")
    print("1. Broker_Primary_File_1.csv (3 records)")
    print("2. Broker_Secondary_File_2.csv (3 records)")
    print("\nTotal: 18 records across 5 files")
    print("\n🚀 You can now run: python dynamic_reconciliation_system.py")
    print("   The system will guide you through:")
    print("   1. Uploading trader and broker files separately")
    print("   2. Dynamic column mapping with dropdowns")
    print("   3. Automatic business logic calculations")
    print("   4. Console sheet generation with summaries")

if __name__ == "__main__":
    create_sample_files()
