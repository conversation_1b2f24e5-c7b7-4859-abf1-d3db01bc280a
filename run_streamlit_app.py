#!/usr/bin/env python3
"""
Quick start script for Streamlit Dynamic Reconciliation System
"""

import subprocess
import sys
import os

def install_requirements():
    """Install required packages"""
    print("📦 Installing required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ All packages installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing packages: {e}")
        return False

def create_sample_data():
    """Create sample data if it doesn't exist"""
    sample_files = [
        'Star_AGL013_Trader_File.csv',
        'CQG_Trader_File.csv',
        'Custom_Trader_File.csv',
        'Broker_Primary_File.csv',
        'Broker_Secondary_File.csv'
    ]

    if not any(os.path.exists(f) for f in sample_files):
        print("🔧 Creating hybrid demo files...")
        try:
            subprocess.check_call([sys.executable, "demo_hybrid_system.py"])
            print("✅ Hybrid demo files created successfully!")
        except subprocess.CalledProcessError as e:
            print(f"❌ Error creating demo files: {e}")
            return False
    else:
        print("✅ Demo files already exist!")

    return True

def run_streamlit_app():
    """Run the Streamlit application"""
    print("🚀 Starting Streamlit Dynamic Reconciliation System...")
    print("🌐 The app will open in your default web browser")
    print("📊 Upload your trader and broker files to get started!")
    print("-" * 60)
    
    try:
        subprocess.run([sys.executable, "-m", "streamlit", "run", "streamlit_reconciliation.py"])
    except KeyboardInterrupt:
        print("\n👋 Application stopped by user")
    except Exception as e:
        print(f"❌ Error running Streamlit app: {e}")

def main():
    """Main execution function"""
    print("🎯 Dynamic Reconciliation System - Streamlit Launcher")
    print("=" * 60)
    
    # Check if we're in the right directory
    if not os.path.exists("streamlit_reconciliation.py"):
        print("❌ streamlit_reconciliation.py not found!")
        print("Please run this script from the RECCON directory")
        return
    
    # Install requirements
    if not install_requirements():
        return
    
    # Create sample data
    if not create_sample_data():
        return
    
    print("\n" + "=" * 60)
    print("🎉 Setup complete! Starting the web application...")
    print("=" * 60)
    
    # Run the Streamlit app
    run_streamlit_app()

if __name__ == "__main__":
    main()
