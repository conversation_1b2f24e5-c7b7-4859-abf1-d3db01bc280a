{"Star AGL013 Profile": {"name": "Star AGL013 Profile", "mappings": {"Date": "Trade Date", "Account": "UCC Code", "State": "State Code", "Contract": "Instrument", "Expiry": "Expiry", "Price": "Trade Price", "Lot": "Lot Size", "Multiplication Factor": "Multiplier", "Commission": "Brokerage", "Venue": "Exchange", "Closing Price": "Close Price", "Trade Type": "Trade Type", "Quantity": "Quantity", "Trader Name": "Trader ID", "Order ID": "Order Number"}, "logic": {"Account": "last_6_digits", "Date": "normalize", "State": "normalize"}, "file_patterns": ["star", "agl013"], "created_date": "2024-02-20T10:00:00", "last_used": "2024-02-20T10:00:00"}, "CQG Trader Profile": {"name": "CQG Trader Profile", "mappings": {"Date": "Date", "Account": "Account", "State": "State", "Contract": "Symbol", "Expiry": "Expiration", "Price": "Price", "Lot": "Lots", "Multiplication Factor": "<PERSON>lt Factor", "Commission": "Commission", "Venue": "Venue", "Closing Price": "Closing Price", "Trade Type": "Side", "Quantity": "Size", "Trader Name": "Trader", "Order ID": "Order ID"}, "logic": {"Account": "first_6_digits", "Date": "normalize", "State": "normalize"}, "file_patterns": ["cqg"], "created_date": "2024-02-20T10:30:00", "last_used": "2024-02-20T10:30:00"}}