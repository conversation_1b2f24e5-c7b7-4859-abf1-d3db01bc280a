try:
    from docx import Document
    
    # Read the Word document
    doc = Document("Task Description.docx")
    
    print("=== TASK DESCRIPTION ===")
    for paragraph in doc.paragraphs:
        if paragraph.text.strip():
            print(paragraph.text)
    
    print("\n=== TABLES (if any) ===")
    for table in doc.tables:
        for row in table.rows:
            row_text = []
            for cell in row.cells:
                row_text.append(cell.text.strip())
            print(" | ".join(row_text))
        print()
        
except ImportError:
    print("python-docx not installed. Installing...")
    import subprocess
    subprocess.run(["pip", "install", "python-docx"])
    
    from docx import Document
    
    # Read the Word document
    doc = Document("Task Description.docx")
    
    print("=== TASK DESCRIPTION ===")
    for paragraph in doc.paragraphs:
        if paragraph.text.strip():
            print(paragraph.text)
    
    print("\n=== TABLES (if any) ===")
    for table in doc.tables:
        for row in table.rows:
            row_text = []
            for cell in row.cells:
                row_text.append(cell.text.strip())
            print(" | ".join(row_text))
        print()
