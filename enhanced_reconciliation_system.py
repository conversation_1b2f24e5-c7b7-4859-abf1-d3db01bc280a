import pandas as pd
from datetime import datetime
import os
import hashlib
import json
from pathlib import Path
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ReconciliationSystem:
    def __init__(self):
        # Standard columns for the final reconciliation sheet
        self.STANDARD_RECON_COLUMNS = [
            "Date",
            "Account Number",
            "State", 
            "Contract",
            "Expiry",
            "Instrument/Symbol",
            "Trade Type",
            "Quantity/Size",
            "Lot",
            "Price",
            "Multiplication Factor",
            "Contract Value",
            "Value/Notional",
            "Commission",
            "Input Source",
            "Venue",
            "Closing Price",
            "Closing Contract Value",
            "Profit/Loss",
            "Broker ID/Code",
            "Trader Name/ID",
            "Order ID/Trade ID"
        ]
        
        # File tracking for duplicate prevention
        self.processed_files_db = "processed_files.json"
        self.processed_files = self.load_processed_files()
        
        # Column mappings storage
        self.mappings_db = "column_mappings.json"
        self.saved_mappings = self.load_saved_mappings()
        
    def load_processed_files(self):
        """Load the database of already processed files"""
        if os.path.exists(self.processed_files_db):
            try:
                with open(self.processed_files_db, 'r') as f:
                    return json.load(f)
            except:
                return {}
        return {}
    
    def save_processed_files(self):
        """Save the processed files database"""
        with open(self.processed_files_db, 'w') as f:
            json.dump(self.processed_files, f, indent=2)
    
    def load_saved_mappings(self):
        """Load saved column mappings"""
        if os.path.exists(self.mappings_db):
            try:
                with open(self.mappings_db, 'r') as f:
                    return json.load(f)
            except:
                return {}
        return {}
    
    def save_mappings(self, mappings):
        """Save column mappings for future use"""
        self.saved_mappings.update(mappings)
        with open(self.mappings_db, 'w') as f:
            json.dump(self.saved_mappings, f, indent=2)
    
    def get_file_hash(self, file_path):
        """Generate hash for file to detect duplicates"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    
    def is_file_processed(self, file_path):
        """Check if file has already been processed"""
        file_hash = self.get_file_hash(file_path)
        file_name = os.path.basename(file_path)
        
        return (file_hash in self.processed_files.values() or 
                file_name in self.processed_files)
    
    def mark_file_processed(self, file_path):
        """Mark file as processed"""
        file_hash = self.get_file_hash(file_path)
        file_name = os.path.basename(file_path)
        self.processed_files[file_name] = file_hash
        self.save_processed_files()
    
    def normalize_date(self, date_str):
        """Enhanced date normalization with more formats"""
        if pd.isna(date_str) or date_str == '':
            return None
        
        try:
            if isinstance(date_str, datetime):
                return date_str.strftime('%Y-%m-%d')
            
            date_str = str(date_str).strip()
            
            # Common date formats
            date_formats = [
                '%Y-%m-%d', '%d/%m/%Y', '%m/%d/%Y', '%d-%m-%Y', '%m-%d-%Y',
                '%d/%m/%y', '%m/%d/%y', '%d-%m-%y', '%m-%d-%y',
                '%d-%b-%Y', '%d-%B-%Y', '%b-%d-%Y', '%B-%d-%Y',
                '%d %b %Y', '%d %B %Y', '%b %d %Y', '%B %d %Y'
            ]
            
            for fmt in date_formats:
                try:
                    return datetime.strptime(date_str, fmt).strftime('%Y-%m-%d')
                except ValueError:
                    continue
            
            # Try pandas parsing as fallback
            return pd.to_datetime(date_str, dayfirst=True).strftime('%Y-%m-%d')
            
        except Exception as e:
            logger.warning(f"Could not normalize date '{date_str}': {e}")
            return None
    
    def extract_account_number(self, account_value, logic_type="direct"):
        """Enhanced account number extraction"""
        if pd.isna(account_value) or account_value == '':
            return None
        
        account_str = str(account_value).strip()
        
        if logic_type == "direct":
            return account_str
        elif logic_type == "last_6_digits":
            return account_str[-6:] if len(account_str) >= 6 else account_str
        elif logic_type == "first_6_digits":
            return account_str[:6] if len(account_str) >= 6 else account_str
        elif logic_type == "last_5_digits":
            return account_str[-5:] if len(account_str) >= 5 else account_str
        elif logic_type == "numeric_only":
            import re
            return re.sub(r'[^0-9]', '', account_str)
        else:
            logger.warning(f"Unknown account extraction logic: {logic_type}")
            return account_str
    
    def calculate_contract_value(self, price, lot, contract, mult_factor):
        """Calculate Contract Value = Price * Lot * Contract * Multiplication Factor"""
        try:
            price = float(price) if pd.notna(price) and price != '' else 0
            lot = float(lot) if pd.notna(lot) and lot != '' else 1
            contract = float(contract) if pd.notna(contract) and contract != '' else 1
            mult_factor = float(mult_factor) if pd.notna(mult_factor) and mult_factor != '' else 1
            
            return price * lot * contract * mult_factor
        except (ValueError, TypeError):
            return 0
    
    def calculate_profit_loss(self, contract_value, closing_contract_value, commission=0):
        """Calculate Profit/Loss"""
        try:
            cv = float(contract_value) if pd.notna(contract_value) else 0
            ccv = float(closing_contract_value) if pd.notna(closing_contract_value) else 0
            comm = float(commission) if pd.notna(commission) else 0

            return ccv - cv - comm
        except (ValueError, TypeError):
            return 0

    def load_file_with_detection(self, file_path):
        """Load file with automatic header detection"""
        try:
            # Try different skip_rows values to find the header
            for skip_rows in range(0, 10):
                try:
                    df = pd.read_csv(file_path, skiprows=skip_rows, nrows=5)
                    if len(df.columns) > 3 and not df.columns[0].startswith('Unnamed'):
                        # Found likely header row
                        df_full = pd.read_csv(file_path, skiprows=skip_rows)
                        logger.info(f"Loaded {file_path} with {skip_rows} rows skipped")
                        return df_full, skip_rows
                except:
                    continue

            # Fallback: load without skipping
            df = pd.read_csv(file_path)
            return df, 0

        except Exception as e:
            logger.error(f"Error loading {file_path}: {e}")
            return None, 0

    def detect_column_mappings(self, df, file_name):
        """Automatically detect potential column mappings"""
        mappings = {}
        columns = [col.lower().strip() for col in df.columns]

        # Define mapping patterns
        mapping_patterns = {
            "Date": ["date", "trade date", "transaction date", "dt"],
            "Account Number": ["account", "acc", "ucc", "client", "account number"],
            "Contract": ["contract", "con", "cont", "symbol", "instrument"],
            "Price": ["price", "rate", "px"],
            "Quantity/Size": ["qty", "quantity", "size", "volume"],
            "Lot": ["lot", "lots", "lot size"],
            "Trade Type": ["side", "buy/sell", "type", "direction"],
            "Commission": ["commission", "comm", "brokerage", "charges"],
            "Venue": ["exchange", "venue", "market"],
            "Order ID/Trade ID": ["order", "trade id", "order no", "ref"]
        }

        for standard_col, patterns in mapping_patterns.items():
            for col in columns:
                for pattern in patterns:
                    if pattern in col:
                        mappings[standard_col] = df.columns[columns.index(col)]
                        break
                if standard_col in mappings:
                    break

        return mappings

    def create_mapping_ui(self, all_files_data):
        """Create UI for column mapping"""
        root = tk.Tk()
        root.title("Column Mapping Configuration")
        root.geometry("800x600")

        mappings = {}

        # Create notebook for different files
        notebook = ttk.Notebook(root)
        notebook.pack(fill='both', expand=True, padx=10, pady=10)

        for file_name, df in all_files_data.items():
            # Create frame for each file
            frame = ttk.Frame(notebook)
            notebook.add(frame, text=file_name[:20] + "..." if len(file_name) > 20 else file_name)

            # Create scrollable frame
            canvas = tk.Canvas(frame)
            scrollbar = ttk.Scrollbar(frame, orient="vertical", command=canvas.yview)
            scrollable_frame = ttk.Frame(canvas)

            scrollable_frame.bind(
                "<Configure>",
                lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
            )

            canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
            canvas.configure(yscrollcommand=scrollbar.set)

            # File mappings
            file_mappings = {}
            mappings[file_name] = file_mappings

            # Auto-detect mappings
            auto_mappings = self.detect_column_mappings(df, file_name)

            # Create mapping dropdowns
            ttk.Label(scrollable_frame, text=f"Map columns for: {file_name}",
                     font=('Arial', 12, 'bold')).grid(row=0, column=0, columnspan=3, pady=10)

            available_columns = [''] + list(df.columns)

            for i, standard_col in enumerate(self.STANDARD_RECON_COLUMNS, 1):
                ttk.Label(scrollable_frame, text=standard_col).grid(row=i, column=0, sticky='w', padx=5, pady=2)

                var = tk.StringVar()
                # Set auto-detected value if available
                if standard_col in auto_mappings:
                    var.set(auto_mappings[standard_col])

                combo = ttk.Combobox(scrollable_frame, textvariable=var, values=available_columns, width=30)
                combo.grid(row=i, column=1, padx=5, pady=2)

                file_mappings[standard_col] = var

            canvas.pack(side="left", fill="both", expand=True)
            scrollbar.pack(side="right", fill="y")

        # Buttons
        button_frame = ttk.Frame(root)
        button_frame.pack(fill='x', padx=10, pady=5)

        result = {'mappings': None, 'cancelled': False}

        def save_mappings():
            final_mappings = {}
            for file_name, file_vars in mappings.items():
                final_mappings[file_name] = {}
                for standard_col, var in file_vars.items():
                    if var.get():  # Only save non-empty mappings
                        final_mappings[file_name][standard_col] = var.get()

            result['mappings'] = final_mappings
            root.quit()

        def cancel():
            result['cancelled'] = True
            root.quit()

        ttk.Button(button_frame, text="Save Mappings", command=save_mappings).pack(side='right', padx=5)
        ttk.Button(button_frame, text="Cancel", command=cancel).pack(side='right', padx=5)

        root.mainloop()
        root.destroy()

        if result['cancelled']:
            return None

        return result['mappings']

    def apply_reconciliation_with_calculations(self, all_files_data, mappings):
        """Apply reconciliation with business logic calculations"""
        consolidated_data = pd.DataFrame(columns=self.STANDARD_RECON_COLUMNS)

        for file_name, df in all_files_data.items():
            if file_name not in mappings:
                logger.warning(f"No mappings found for {file_name}")
                continue

            file_mappings = mappings[file_name]
            file_records = []

            for index, row in df.iterrows():
                recon_record = {}

                # Apply basic mappings
                for standard_col in self.STANDARD_RECON_COLUMNS:
                    source_col = file_mappings.get(standard_col)

                    if source_col and source_col in row:
                        value = row[source_col]

                        # Apply transformations based on column type
                        if standard_col == "Date":
                            recon_record[standard_col] = self.normalize_date(value)
                        elif standard_col == "Account Number":
                            recon_record[standard_col] = self.extract_account_number(value, "direct")
                        else:
                            recon_record[standard_col] = value
                    else:
                        recon_record[standard_col] = None

                # Apply business logic calculations
                self.apply_business_calculations(recon_record, row, file_mappings)

                # Add metadata
                recon_record["Input Source"] = file_name

                file_records.append(recon_record)

            # Convert to DataFrame and concatenate
            if file_records:
                file_df = pd.DataFrame(file_records)
                consolidated_data = pd.concat([consolidated_data, file_df], ignore_index=True)
                logger.info(f"Processed {len(file_records)} records from {file_name}")

        return consolidated_data

    def apply_business_calculations(self, recon_record, source_row, mappings):
        """Apply business logic calculations"""
        try:
            # Contract Value calculation
            price = recon_record.get("Price", 0)
            lot = recon_record.get("Lot", 1)
            contract = recon_record.get("Contract", 1)
            mult_factor = recon_record.get("Multiplication Factor", 1)

            contract_value = self.calculate_contract_value(price, lot, contract, mult_factor)
            recon_record["Contract Value"] = contract_value

            # Closing Contract Value calculation (if closing price available)
            closing_price = recon_record.get("Closing Price")
            if closing_price:
                closing_contract_value = self.calculate_contract_value(
                    closing_price, lot, contract, mult_factor
                )
                recon_record["Closing Contract Value"] = closing_contract_value

                # Profit/Loss calculation
                commission = recon_record.get("Commission", 0)
                profit_loss = self.calculate_profit_loss(
                    contract_value, closing_contract_value, commission
                )
                recon_record["Profit/Loss"] = profit_loss

            # Additional business logic can be added here

        except Exception as e:
            logger.error(f"Error in business calculations: {e}")

    def process_files(self, file_paths):
        """Main processing function"""
        logger.info(f"Starting reconciliation process for {len(file_paths)} files")

        # Check for duplicates
        new_files = []
        for file_path in file_paths:
            if self.is_file_processed(file_path):
                logger.warning(f"File {file_path} already processed. Skipping.")
            else:
                new_files.append(file_path)

        if not new_files:
            logger.info("No new files to process")
            return None

        # Load all files
        all_files_data = {}
        for file_path in new_files:
            df, skip_rows = self.load_file_with_detection(file_path)
            if df is not None:
                file_name = os.path.basename(file_path)
                all_files_data[file_name] = df
                logger.info(f"Loaded {file_name}: {len(df)} rows, {len(df.columns)} columns")

        if not all_files_data:
            logger.error("No files could be loaded")
            return None

        # Get column mappings
        mappings = self.create_mapping_ui(all_files_data)
        if mappings is None:
            logger.info("User cancelled mapping process")
            return None

        # Save mappings for future use
        self.save_mappings(mappings)

        # Apply reconciliation
        consolidated_df = self.apply_reconciliation_with_calculations(all_files_data, mappings)

        # Mark files as processed
        for file_path in new_files:
            self.mark_file_processed(file_path)

        logger.info(f"Reconciliation complete. {len(consolidated_df)} total records")
        return consolidated_df

    def save_console_sheet(self, consolidated_df, output_path="Console_Reconciliation.xlsx"):
        """Save the final console sheet"""
        try:
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                consolidated_df.to_excel(writer, sheet_name='Console', index=False)

                # Auto-adjust column widths
                worksheet = writer.sheets['Console']
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width

            logger.info(f"Console sheet saved to {output_path}")
            return True
        except Exception as e:
            logger.error(f"Error saving console sheet: {e}")
            return False

def create_file_selection_ui():
    """Create UI for file selection"""
    root = tk.Tk()
    root.title("Reconciliation System - File Selection")
    root.geometry("600x400")

    selected_files = []

    # Main frame
    main_frame = ttk.Frame(root, padding="10")
    main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

    # Title
    ttk.Label(main_frame, text="Brokers vs Traders Reconciliation System",
              font=('Arial', 14, 'bold')).grid(row=0, column=0, columnspan=2, pady=10)

    # File list
    ttk.Label(main_frame, text="Selected Files:").grid(row=1, column=0, sticky='w', pady=5)

    file_listbox = tk.Listbox(main_frame, height=10, width=70)
    file_listbox.grid(row=2, column=0, columnspan=2, pady=5, sticky=(tk.W, tk.E))

    # Scrollbar for listbox
    scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=file_listbox.yview)
    scrollbar.grid(row=2, column=2, sticky=(tk.N, tk.S))
    file_listbox.configure(yscrollcommand=scrollbar.set)

    def add_files():
        files = filedialog.askopenfilenames(
            title="Select Trader/Broker Files",
            filetypes=[
                ("Excel files", "*.xlsx *.xls"),
                ("CSV files", "*.csv"),
                ("All files", "*.*")
            ]
        )
        for file in files:
            if file not in selected_files:
                selected_files.append(file)
                file_listbox.insert(tk.END, os.path.basename(file))

    def remove_file():
        selection = file_listbox.curselection()
        if selection:
            index = selection[0]
            file_listbox.delete(index)
            selected_files.pop(index)

    def clear_all():
        file_listbox.delete(0, tk.END)
        selected_files.clear()

    # Buttons
    button_frame = ttk.Frame(main_frame)
    button_frame.grid(row=3, column=0, columnspan=2, pady=10)

    ttk.Button(button_frame, text="Add Files", command=add_files).pack(side='left', padx=5)
    ttk.Button(button_frame, text="Remove Selected", command=remove_file).pack(side='left', padx=5)
    ttk.Button(button_frame, text="Clear All", command=clear_all).pack(side='left', padx=5)

    # Process button
    result = {'files': None, 'cancelled': False}

    def process_files():
        if not selected_files:
            messagebox.showwarning("No Files", "Please select at least one file to process.")
            return
        result['files'] = selected_files.copy()
        root.quit()

    def cancel():
        result['cancelled'] = True
        root.quit()

    process_frame = ttk.Frame(main_frame)
    process_frame.grid(row=4, column=0, columnspan=2, pady=20)

    ttk.Button(process_frame, text="Process Files", command=process_files,
               style='Accent.TButton').pack(side='right', padx=5)
    ttk.Button(process_frame, text="Cancel", command=cancel).pack(side='right', padx=5)

    # Configure grid weights
    root.columnconfigure(0, weight=1)
    root.rowconfigure(0, weight=1)
    main_frame.columnconfigure(0, weight=1)
    main_frame.rowconfigure(2, weight=1)

    root.mainloop()
    root.destroy()

    if result['cancelled']:
        return None
    return result['files']

def main():
    """Main function"""
    print("🚀 Starting Brokers vs Traders Reconciliation System")
    print("=" * 60)

    # Initialize system
    recon_system = ReconciliationSystem()

    try:
        # Get files to process
        file_paths = create_file_selection_ui()

        if file_paths is None:
            print("❌ Process cancelled by user")
            return

        if not file_paths:
            print("❌ No files selected")
            return

        print(f"📁 Selected {len(file_paths)} files for processing")

        # Process files
        consolidated_df = recon_system.process_files(file_paths)

        if consolidated_df is None:
            print("❌ Processing failed or was cancelled")
            return

        # Save results
        output_file = f"Console_Reconciliation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        success = recon_system.save_console_sheet(consolidated_df, output_file)

        if success:
            print(f"✅ Reconciliation completed successfully!")
            print(f"📊 Processed {len(consolidated_df)} total records")
            print(f"💾 Results saved to: {output_file}")

            # Show summary
            print("\n📈 Summary by Input Source:")
            source_summary = consolidated_df.groupby('Input Source').size()
            for source, count in source_summary.items():
                print(f"   {source}: {count} records")
        else:
            print("❌ Failed to save results")

    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        print(f"❌ An error occurred: {e}")

if __name__ == "__main__":
    main()
