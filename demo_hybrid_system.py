#!/usr/bin/env python3
"""
Demo script for Hybrid Dynamic Reconciliation System
Creates realistic sample data that demonstrates the hybrid approach
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import random
import os

def create_star_agl013_trader_file():
    """Create a realistic Star AGL013 trader file with specific column names"""
    np.random.seed(42)
    
    # Star AGL013 specific column names (realistic variations)
    data = {
        'Trade Date': [
            '14-Feb-2024', '15-Feb-2024', '16-Feb-2024', '19-Feb-2024', '20-Feb-2024'
        ],
        'UCC Code': [
            'STAR001234567', 'STAR001234568', 'STAR001234569', 'STAR001234570', 'STAR001234571'
        ],
        'State Code': ['MH', 'gj', 'maha', 'Maharashtra', 'GJ'],
        'Instrument': ['NIFTY', 'BANKNIFTY', 'SENSEX', 'NIFTY', 'BANKNIFTY'],
        'Expiry': ['28-Mar-2024', '28-Mar-2024', '28-Mar-2024', '25-Apr-2024', '25-Apr-2024'],
        'Trade Price': [21500.50, 47800.25, 72150.75, 21600.00, 47900.50],
        'Lot Size': [50, 25, 10, 50, 25],
        'Contract Size': [1, 1, 1, 2, 1],
        'Multiplier': [1, 1, 1, 1, 1],
        'Brokerage': [25.50, 45.75, 15.25, 30.00, 40.25],
        'Exchange': ['NSE', 'NSE', 'BSE', 'NSE', 'NSE'],
        'Close Price': [21550.75, 47850.00, 72200.25, 21580.50, 47920.75],
        'Trade Type': ['BUY', 'SELL', 'BUY', 'SELL', 'BUY'],
        'Quantity': [50, 25, 10, 100, 25],
        'Trader ID': ['TR001', 'TR002', 'TR003', 'TR001', 'TR002'],
        'Order Number': ['ORD2024001', 'ORD2024002', 'ORD2024003', 'ORD2024004', 'ORD2024005']
    }
    
    df = pd.DataFrame(data)
    df.to_csv('Star_AGL013_Trader_File.csv', index=False)
    print("✅ Created Star_AGL013_Trader_File.csv")
    return df

def create_cqg_trader_file():
    """Create a CQG trader file with different column naming convention"""
    np.random.seed(43)
    
    # CQG specific column names
    data = {
        'Date': ['2024-02-14', '2024-02-15', '2024-02-16', '2024-02-19', '2024-02-20'],
        'Account': ['CQG123456', 'CQG123457', 'CQG123458', 'CQG123459', 'CQG123460'],
        'State': ['Maharashtra', 'Gujarat', 'Delhi', 'Karnataka', 'Tamil Nadu'],
        'Symbol': ['NIFTY24MAR', 'BANKNIFTY24MAR', 'SENSEX24MAR', 'NIFTY24APR', 'BANKNIFTY24APR'],
        'Expiration': ['2024-03-28', '2024-03-28', '2024-03-28', '2024-04-25', '2024-04-25'],
        'Price': [21480.25, 47750.50, 72100.00, 21620.75, 47880.25],
        'Lots': [75, 30, 15, 60, 35],
        'Contracts': [1, 1, 1, 1, 2],
        'Mult Factor': [1, 1, 1, 1, 1],
        'Commission': [35.75, 52.25, 22.50, 42.00, 48.75],
        'Venue': ['NSE', 'NSE', 'BSE', 'NSE', 'NSE'],
        'Closing Price': [21520.50, 47800.75, 72150.25, 21600.25, 47900.00],
        'Side': ['Long', 'Short', 'Long', 'Short', 'Long'],
        'Size': [75, 30, 15, 60, 70],
        'Trader': ['CQG_TR01', 'CQG_TR02', 'CQG_TR03', 'CQG_TR01', 'CQG_TR02'],
        'Order ID': ['CQG001', 'CQG002', 'CQG003', 'CQG004', 'CQG005']
    }
    
    df = pd.DataFrame(data)
    df.to_csv('CQG_Trader_File.csv', index=False)
    print("✅ Created CQG_Trader_File.csv")
    return df

def create_custom_trader_file():
    """Create a custom trader file with yet another column format"""
    np.random.seed(44)
    
    # Custom format with mixed naming
    data = {
        'Transaction Date': ['14/02/2024', '15/02/2024', '16/02/2024', '19/02/2024', '20/02/2024'],
        'Client Account ID': ['CUST987654321', 'CUST987654322', 'CUST987654323', 'CUST987654324', 'CUST987654325'],
        'Location': ['mh', 'guj', 'dl', 'kar', 'tn'],
        'Product': ['NIFTY FUT', 'BANKNIFTY FUT', 'SENSEX FUT', 'NIFTY FUT', 'BANKNIFTY FUT'],
        'Maturity': ['28/03/2024', '28/03/2024', '28/03/2024', '25/04/2024', '25/04/2024'],
        'Execution Price': [21470.00, 47720.25, 72080.50, 21590.75, 47860.00],
        'Lot Quantity': [40, 20, 8, 45, 22],
        'Contract Multiplier': [1, 1, 1, 1, 1],
        'Factor': [1, 1, 1, 1, 1],
        'Fees': [28.50, 38.75, 18.25, 35.00, 42.50],
        'Market': ['NSE', 'NSE', 'BSE', 'NSE', 'NSE'],
        'Settlement Price': [21510.25, 47770.50, 72130.75, 21570.00, 47890.25],
        'Direction': ['Buy', 'Sell', 'Buy', 'Sell', 'Buy'],
        'Volume': [40, 20, 8, 45, 44],
        'Dealer': ['DEAL01', 'DEAL02', 'DEAL03', 'DEAL01', 'DEAL02'],
        'Reference': ['REF001', 'REF002', 'REF003', 'REF004', 'REF005']
    }
    
    df = pd.DataFrame(data)
    df.to_csv('Custom_Trader_File.csv', index=False)
    print("✅ Created Custom_Trader_File.csv")
    return df

def create_broker_primary_file():
    """Create a primary broker file with broker-specific columns"""
    np.random.seed(45)
    
    # Broker primary format
    data = {
        'Settlement Date': ['2024-02-14', '2024-02-15', '2024-02-16', '2024-02-19', '2024-02-20'],
        'Member Account': ['BRK001234567890', 'BRK001234567891', 'BRK001234567892', 'BRK001234567893', 'BRK001234567894'],
        'Region': ['Maharashtra', 'Gujarat', 'Delhi', 'Karnataka', 'Tamil Nadu'],
        'Security': ['NIFTY24MAR21500CE', 'BANKNIFTY24MAR47800PE', 'SENSEX24MAR72100CE', 'NIFTY24APR21600CE', 'BANKNIFTY24APR47900PE'],
        'Expiry Date': ['28-Mar-24', '28-Mar-24', '28-Mar-24', '25-Apr-24', '25-Apr-24'],
        'Deal Price': [21485.75, 47765.25, 72095.50, 21605.00, 47875.75],
        'Lot Count': [60, 28, 12, 55, 30],
        'Contract Units': [1, 1, 1, 1, 1],
        'Multiplication': [1, 1, 1, 1, 1],
        'Brokerage Charges': [32.25, 48.50, 20.75, 38.00, 45.25],
        'Trading Venue': ['NSE', 'NSE', 'BSE', 'NSE', 'NSE'],
        'Mark to Market': [21525.00, 47815.75, 72145.25, 21585.50, 47905.00],
        'Transaction Type': ['Purchase', 'Sale', 'Purchase', 'Sale', 'Purchase'],
        'Trade Quantity': [60, 28, 12, 55, 60],
        'Broker Rep': ['BR001', 'BR002', 'BR003', 'BR001', 'BR002'],
        'Trade Ref': ['TRD2024001', 'TRD2024002', 'TRD2024003', 'TRD2024004', 'TRD2024005']
    }
    
    df = pd.DataFrame(data)
    df.to_csv('Broker_Primary_File.csv', index=False)
    print("✅ Created Broker_Primary_File.csv")
    return df

def create_broker_secondary_file():
    """Create a secondary broker file with different format"""
    np.random.seed(46)
    
    # Broker secondary format
    data = {
        'Trade_Date': ['14-Feb-2024', '15-Feb-2024', '16-Feb-2024', '19-Feb-2024', '20-Feb-2024'],
        'Account_Number': ['SEC987654321098', 'SEC987654321099', 'SEC987654321100', 'SEC987654321101', 'SEC987654321102'],
        'State_Code': ['MH', 'GJ', 'DL', 'KA', 'TN'],
        'Instrument_Name': ['NIFTY', 'BANKNIFTY', 'SENSEX', 'NIFTY', 'BANKNIFTY'],
        'Expiry_Date': ['28/03/2024', '28/03/2024', '28/03/2024', '25/04/2024', '25/04/2024'],
        'Trade_Price': [21475.50, 47740.75, 72085.25, 21595.25, 47865.50],
        'Lot_Size': [45, 22, 9, 50, 26],
        'Contract_Size': [1, 1, 1, 1, 1],
        'Multiplier_Factor': [1, 1, 1, 1, 1],
        'Commission_Amount': [30.00, 41.25, 19.50, 36.75, 44.00],
        'Exchange_Name': ['NSE', 'NSE', 'BSE', 'NSE', 'NSE'],
        'Closing_Rate': [21515.75, 47785.25, 72135.50, 21575.75, 47895.75],
        'Buy_Sell': ['B', 'S', 'B', 'S', 'B'],
        'Traded_Qty': [45, 22, 9, 50, 52],
        'Broker_Name': ['SEC_BR01', 'SEC_BR02', 'SEC_BR03', 'SEC_BR01', 'SEC_BR02'],
        'Order_Ref': ['SEC001', 'SEC002', 'SEC003', 'SEC004', 'SEC005']
    }
    
    df = pd.DataFrame(data)
    df.to_csv('Broker_Secondary_File.csv', index=False)
    print("✅ Created Broker_Secondary_File.csv")
    return df

def create_mapping_profiles_demo():
    """Create sample mapping profiles to demonstrate the hybrid approach"""
    profiles = {
        "Star AGL013 Profile": {
            "name": "Star AGL013 Profile",
            "mappings": {
                "Date": "Trade Date",
                "Account": "UCC Code", 
                "State": "State Code",
                "Contract": "Instrument",
                "Expiry": "Expiry",
                "Price": "Trade Price",
                "Lot": "Lot Size",
                "Multiplication Factor": "Multiplier",
                "Commission": "Brokerage",
                "Venue": "Exchange",
                "Closing Price": "Close Price",
                "Trade Type": "Trade Type",
                "Quantity": "Quantity",
                "Trader Name": "Trader ID",
                "Order ID": "Order Number"
            },
            "logic": {
                "Account": "last_6_digits",
                "Date": "normalize",
                "State": "normalize"
            },
            "file_patterns": ["star", "agl013"],
            "created_date": "2024-02-20T10:00:00",
            "last_used": "2024-02-20T10:00:00"
        },
        "CQG Trader Profile": {
            "name": "CQG Trader Profile", 
            "mappings": {
                "Date": "Date",
                "Account": "Account",
                "State": "State", 
                "Contract": "Symbol",
                "Expiry": "Expiration",
                "Price": "Price",
                "Lot": "Lots",
                "Multiplication Factor": "Mult Factor",
                "Commission": "Commission",
                "Venue": "Venue",
                "Closing Price": "Closing Price",
                "Trade Type": "Side",
                "Quantity": "Size",
                "Trader Name": "Trader",
                "Order ID": "Order ID"
            },
            "logic": {
                "Account": "first_6_digits",
                "Date": "normalize",
                "State": "normalize"
            },
            "file_patterns": ["cqg"],
            "created_date": "2024-02-20T10:30:00",
            "last_used": "2024-02-20T10:30:00"
        }
    }
    
    import json
    with open('mapping_profiles.json', 'w') as f:
        json.dump(profiles, f, indent=2)
    
    print("✅ Created sample mapping profiles (mapping_profiles.json)")

def main():
    """Create all demo files"""
    print("🎯 Creating Hybrid System Demo Files")
    print("=" * 50)
    
    # Create trader files with different formats
    print("\n📁 Creating Trader Files:")
    create_star_agl013_trader_file()
    create_cqg_trader_file() 
    create_custom_trader_file()
    
    # Create broker files with different formats
    print("\n📁 Creating Broker Files:")
    create_broker_primary_file()
    create_broker_secondary_file()
    
    # Create sample mapping profiles
    print("\n🎯 Creating Sample Mapping Profiles:")
    create_mapping_profiles_demo()
    
    print("\n" + "=" * 50)
    print("🎉 Demo files created successfully!")
    print("\n📊 Files created:")
    print("   • Star_AGL013_Trader_File.csv (Star format)")
    print("   • CQG_Trader_File.csv (CQG format)")
    print("   • Custom_Trader_File.csv (Custom format)")
    print("   • Broker_Primary_File.csv (Primary broker format)")
    print("   • Broker_Secondary_File.csv (Secondary broker format)")
    print("   • mapping_profiles.json (Sample profiles)")
    
    print("\n🚀 Next Steps:")
    print("   1. Run: python run_streamlit_app.py")
    print("   2. Upload the demo files")
    print("   3. Try loading the sample profiles")
    print("   4. See the hybrid approach in action!")

if __name__ == "__main__":
    main()
