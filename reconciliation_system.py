import pandas as pd
from datetime import datetime
import os

# --- Configuration ---
# Define the standard columns for your final reconciliation sheet
STANDARD_RECON_COLUMNS = [
    "Date",
    "Account Number",
    "State",
    "Instrument/Symbol",
    "Trade Type",
    "Quantity/Size",
    "Price",
    "Value/Notional",
    "Broker ID/Code",
    "Trader Name/ID",
    "Order ID/Trade ID"
]

# Paths to your uploaded CSV files and their estimated rows above header
# These are derived from your file uploads and should be adjusted if needed.
# Note: 'estimatedRowsAboveHeader' refers to skiprows in pandas.read_csv
FILE_CONFIGS = [
    {"path": "Recon For reference.xlsx - Sheet10.csv", "skiprows": 1},
    {"path": "Recon For reference.xlsx - Star AGL013 Trade File - 24 Apr.csv", "skiprows": 4},
    {"path": "Recon For reference.xlsx - Star AGL07 Trade File - 24 Apr .csv", "skiprows": 4},
    {"path": "Recon For reference.xlsx - CQG Trader (Surajb) Trade File .csv", "skiprows": 0},
    {"path": "Recon For reference.xlsx - Star01 Trade File- 24 Apr 25.csv", "skiprows": 0},
    {"path": "Recon For reference.xlsx - Console.csv", "skiprows": 0}, # Assuming Console.csv is a source for now
    {"path": "Recon For reference.xlsx - Sheet7.csv", "skiprows": 0},
    {"path": "Recon For reference.xlsx - EOD Trading Activity.csv", "skiprows": 0},
]

# --- Data Transformation Logic (Placeholders) ---

def normalize_date(date_str):
    """
    Normalizes various date formats (e.g., '14-Feb', '21/01/25', '25th March 2005')
    into a consistent 'YYYY-MM-DD' format.
    """
    if pd.isna(date_str):
        return None
    try:
        # Attempt to parse common formats
        if isinstance(date_str, datetime): # Already a datetime object
            return date_str.strftime('%Y-%m-%d')
        elif '/' in date_str: # e.g., 21/01/25
            return pd.to_datetime(date_str, dayfirst=True).strftime('%Y-%m-%d')
        elif '-' in date_str: # e.g., 14-Feb, 14-Feb-2024
            return pd.to_datetime(date_str).strftime('%Y-%m-%d')
        elif 'th ' in date_str or 'rd ' in date_str or 'st ' in date_str or 'nd ' in date_str: # e.g., 25th March 2005
            # Remove ordinal suffixes (st, nd, rd, th)
            date_str = date_str.replace('st', '').replace('nd', '').replace('rd', '').replace('th', '')
            return pd.to_datetime(date_str).strftime('%Y-%m-%d')
        else:
            # Try a generic parse if specific patterns fail
            return pd.to_datetime(date_str).strftime('%Y-%m-%d')
    except Exception as e:
        print(f"Warning: Could not normalize date '{date_str}'. Error: {e}")
        return None # Return None or original string if normalization fails

def extract_account_number(account_value, logic_type="direct"):
    """
    Extracts and standardizes account numbers based on specified logic.
    Logic types: "direct", "last_6_digits", "first_6_digits".
    If 'account_value' comes from a column like 'W' and contains other data,
    you'd need to parse that column specifically. Here, we assume 'account_value'
    is already the relevant string.
    """
    if pd.isna(account_value):
        return None

    account_str = str(account_value).strip()

    if logic_type == "direct":
        return account_str
    elif logic_type == "last_6_digits":
        return account_str[-6:] if len(account_str) >= 6 else account_str
    elif logic_type == "first_6_digits":
        return account_str[:6] if len(account_str) >= 6 else account_str
    else:
        print(f"Warning: Unknown account extraction logic type '{logic_type}'. Returning direct value.")
        return account_str

def standardize_state(state_value):
    """
    Converts various state abbreviations/names (e.g., 'MH', 'Mah', 'maha')
    into a consistent format (e.g., 'Maharashtra').
    """
    if pd.isna(state_value):
        return None

    state_str = str(state_value).strip().lower() # Convert to lowercase for consistent matching

    # Define your state mapping here. This can be expanded.
    state_mapping = {
        "mh": "Maharashtra",
        "mah": "Maharashtra",
        "maha": "Maharashtra",
        "maharashtra": "Maharashtra",
        "dl": "Delhi",
        "del": "Delhi",
        "delhi": "Delhi",
        # Add more mappings as needed
    }

    return state_mapping.get(state_str, state_value) # Return standardized state or original if not found

# --- Core Reconciliation Functions ---

def load_all_files(file_configs):
    """
    Loads multiple CSV files into a dictionary of DataFrames,
    handling skiprows for headers.
    """
    loaded_data = {}
    for config in file_configs:
        file_path = config["path"]
        skip_rows = config.get("skiprows", 0) # Default to 0 if not specified
        try:
            df = pd.read_csv(file_path, skiprows=skip_rows)
            file_name = os.path.basename(file_path)
            loaded_data[file_name] = df
            print(f"Loaded '{file_name}' with {len(df.columns)} columns.")
        except FileNotFoundError:
            print(f"Error: File not found at '{file_path}'. Skipping.")
        except Exception as e:
            print(f"Error loading '{file_path}': {e}. Skipping.")
    return loaded_data

def get_all_unique_columns(loaded_data):
    """
    Collects all unique column names from all loaded DataFrames.
    """
    all_columns = set()
    for file_name, df in loaded_data.items():
        all_columns.update(df.columns.tolist())
    return sorted(list(all_columns))

def generate_mapping_template(all_unique_columns):
    """
    Generates a template for mapping standard recon columns to available source columns.
    """
    mapping_template = {}
    for recon_col in STANDARD_RECON_COLUMNS:
        mapping_template[recon_col] = all_unique_columns # Initially, offer all unique columns
    return mapping_template

def apply_reconciliation(loaded_data, user_mappings):
    """
    Applies user-defined mappings and transformation logic to create a
    consolidated reconciliation DataFrame.
    `user_mappings` is a dictionary: {'Standard Recon Column': {'source_file': 'Source Column Name', 'logic': 'logic_type'}}
    Example:
    user_mappings = {
        "Date": {"Recon For reference.xlsx - Star AGL013 Trade File - 24 Apr.csv": "Trade Date"},
        "Account Number": {"Recon For reference.xlsx - Star AGL013 Trade File - 24 Apr.csv": "UCC Code", "logic": "direct"},
        "State": {"Recon For reference.xlsx - Star AGL013 Trade File - 24 Apr.csv": "State"},
        # ... other mappings for different files and columns
    }
    For simplicity in this base script, we'll assume a flat mapping initially
    and then explain how to extend it for file-specific mappings.
    """
    consolidated_data = pd.DataFrame(columns=STANDARD_RECON_COLUMNS)

    for file_name, df in loaded_data.items():
        temp_df = pd.DataFrame()
        # Create a row for each standard recon column
        for recon_col in STANDARD_RECON_COLUMNS:
            mapping_info = user_mappings.get(recon_col)

            if not mapping_info:
                print(f"Warning: No mapping found for '{recon_col}'. Skipping.")
                temp_df[recon_col] = None
                continue

            # This part simulates how you'd get the actual source column name
            # In a real UI, the user would specify which file and which column for each recon_col
            # For this script, we'll assume a simpler user_mappings where the value is just the source column name
            # and we try to find it in the current df. A more robust system would map file->column.

            # Example: user_mappings = {"Date": "Trade Date", "Account Number": "UCC Code"}
            source_col = mapping_info
            logic_type = "direct" # Default logic type

            # If user_mappings provides a dict for advanced logic
            if isinstance(mapping_info, dict):
                source_col = mapping_info.get('source_column')
                logic_type = mapping_info.get('logic', 'direct')
                # Check if this mapping applies to the current file
                if 'file_name' in mapping_info and mapping_info['file_name'] != file_name:
                    continue # Skip if mapping is for a different file

            if source_col and source_col in df.columns:
                series = df[source_col]
                # Apply specific transformations
                if recon_col == "Date":
                    temp_df[recon_col] = series.apply(normalize_date)
                elif recon_col == "Account Number":
                    temp_df[recon_col] = series.apply(lambda x: extract_account_number(x, logic_type))
                elif recon_col == "State":
                    temp_df[recon_col] = series.apply(standardize_state)
                else:
                    temp_df[recon_col] = series # Direct mapping for other columns
            else:
                temp_df[recon_col] = None # Column not found in this file or no mapping
                # print(f"Note: Source column '{source_col}' for recon '{recon_col}' not found in '{file_name}'.")

        # Append data from the current file to the consolidated DataFrame
        # Ensure that the column names match STANDARD_RECON_COLUMNS before concat
        # We need to make sure the temp_df has the correct structure to concatenate
        # A more robust approach would be to create a new row for each row in the original df
        # and then append, ensuring all standard columns are present.

        # Correct way to append: iterate through rows of original df and build the new structure
        file_records = []
        for index, row in df.iterrows():
            recon_record = {}
            for recon_col in STANDARD_RECON_COLUMNS:
                mapping_info = user_mappings.get(recon_col)
                if not mapping_info:
                    recon_record[recon_col] = None
                    continue

                source_col = mapping_info
                logic_type = "direct"

                if isinstance(mapping_info, dict):
                    source_col = mapping_info.get('source_column')
                    logic_type = mapping_info.get('logic', 'direct')
                    if 'file_name' in mapping_info and mapping_info['file_name'] != file_name:
                        continue # This mapping is for another file

                if source_col and source_col in row:
                    value = row[source_col]
                    if recon_col == "Date":
                        recon_record[recon_col] = normalize_date(value)
                    elif recon_col == "Account Number":
                        recon_record[recon_col] = extract_account_number(value, logic_type)
                    elif recon_col == "State":
                        recon_record[recon_col] = standardize_state(value)
                    else:
                        recon_record[recon_col] = value
                else:
                    recon_record[recon_col] = None # Column not found or no mapping for this file/column combination
            file_records.append(recon_record)

        # Convert list of records to DataFrame and concatenate
        if file_records:
            consolidated_data = pd.concat([consolidated_data, pd.DataFrame(file_records)], ignore_index=True)

    return consolidated_data

# --- Main Execution ---
if __name__ == "__main__":
    print("--- Step 1: Loading files ---")
    loaded_dfs = load_all_files(FILE_CONFIGS)
    print("\n")

    if not loaded_dfs:
        print("No files loaded. Exiting.")
    else:
        print("--- Step 2: Collecting all unique source columns ---")
        all_cols = get_all_unique_columns(loaded_dfs)
        print(f"Found {len(all_cols)} unique columns across all files.")
        # print("Unique columns:", all_cols) # Uncomment to see all unique columns
        print("\n")

        print("--- Step 3: Generating Recon Column Mapping Template ---")
        recon_template = generate_mapping_template(all_cols)
        # For demonstration, print a subset or the full template for user review
        print("Recon Column Mapping Template (User will select from these options):")
        for recon_col, source_options in recon_template.items():
            print(f"- {recon_col}: Sample options: {source_options[:5]}...") # Show first 5 options
        print("\n")

        # --- Step 4: Simulating User Input for Mappings ---
        # In a real application, this would come from a UI.
        # This is a DUMMY MAPPING for demonstration. You will need to adjust this
        # based on the actual columns in your files and how you want to map them.
        # This example assumes a single source column per recon field,
        # but a robust solution would allow file-specific mappings.

        # Example of a simpler user mapping (if a column name is consistent across files)
        # user_selected_mappings = {
        #     "Date": "Trade Date", # Assuming 'Trade Date' exists in relevant files
        #     "Account Number": "UCC Code",
        #     "State": "State",
        #     "Instrument/Symbol": "Symbol",
        #     "Trade Type": "Buy/Sell",
        #     "Quantity/Size": "Qty",
        #     "Price": "Price",
        #     "Value/Notional": "Traded Value",
        #     "Broker ID/Code": "Exchange",
        #     "Trader Name/ID": "Client Name",
        #     "Order ID/Trade ID": "Order No."
        # }

        # More robust user mapping structure, specifying file and logic (if needed)
        # This structure allows different files to map to different source columns
        # for the same standard recon column.
        user_selected_mappings = {
            # Mappings for 'Recon For reference.xlsx - Star AGL013 Trade File - 24 Apr.csv'
            "Date": {"file_name": "Recon For reference.xlsx - Star AGL013 Trade File - 24 Apr.csv", "source_column": "Trade Date"},
            "Account Number": {"file_name": "Recon For reference.xlsx - Star AGL013 Trade File - 24 Apr.csv", "source_column": "UCC Code", "logic": "direct"},
            "State": {"file_name": "Recon For reference.xlsx - Star AGL013 Trade File - 24 Apr.csv", "source_column": "State"},
            "Instrument/Symbol": {"file_name": "Recon For reference.xlsx - Star AGL013 Trade File - 24 Apr.csv", "source_column": "Symbol"},
            "Trade Type": {"file_name": "Recon For reference.xlsx - Star AGL013 Trade File - 24 Apr.csv", "source_column": "Buy/Sell"},
            "Quantity/Size": {"file_name": "Recon For reference.xlsx - Star AGL013 Trade File - 24 Apr.csv", "source_column": "Qty"},
            "Price": {"file_name": "Recon For reference.xlsx - Star AGL013 Trade File - 24 Apr.csv", "source_column": "Price"},
            "Value/Notional": {"file_name": "Recon For reference.xlsx - Star AGL013 Trade File - 24 Apr.csv", "source_column": "Traded Value"},
            "Broker ID/Code": {"file_name": "Recon For reference.xlsx - Star AGL013 Trade File - 24 Apr.csv", "source_column": "Exchange"},
            "Trader Name/ID": {"file_name": "Recon For reference.xlsx - Star AGL013 Trade File - 24 Apr.csv", "source_column": "Client Name"},
            "Order ID/Trade ID": {"file_name": "Recon For reference.xlsx - Star AGL013 Trade File - 24 Apr.csv", "source_column": "Order No."},

            # Mappings for 'Recon For reference.xlsx - CQG Trader (Surajb) Trade File .csv'
            "Date": {"file_name": "Recon For reference.xlsx - CQG Trader (Surajb) Trade File .csv", "source_column": "Date"},
            "Account Number": {"file_name": "Recon For reference.xlsx - CQG Trader (Surajb) Trade File .csv", "source_column": "Account"},
            "Instrument/Symbol": {"file_name": "Recon For reference.xlsx - CQG Trader (Surajb) Trade File .csv", "source_column": "Symbol"},
            "Trade Type": {"file_name": "Recon For reference.xlsx - CQG Trader (Surajb) Trade File .csv", "source_column": "Side"},
            "Quantity/Size": {"file_name": "Recon For reference.xlsx - CQG Trader (Surajb) Trade File .csv", "source_column": "Quantity"},
            "Price": {"file_name": "Recon For reference.xlsx - CQG Trader (Surajb) Trade File .csv", "source_column": "Price"},
            "Value/Notional": {"file_name": "Recon For reference.xlsx - CQG Trader (Surajb) Trade File .csv", "source_column": "Value"},
            "Trader Name/ID": {"file_name": "Recon For reference.xlsx - CQG Trader (Surajb) Trade File .csv", "source_column": "Trader"},
            "Order ID/Trade ID": {"file_name": "Recon For reference.xlsx - CQG Trader (Surajb) Trade File .csv", "source_column": "Order ID"},

            # Add mappings for other files here following the same structure
            # If a column name is consistent across files AND its logic is also consistent,
            # you can map it once without specifying file_name. The script would need a small
            # adjustment to prioritize file-specific mappings over generic ones.
        }

        print("--- Step 5: Applying Reconciliation Logic ---")
        final_recon_df = apply_reconciliation(loaded_dfs, user_selected_mappings)

        print("\n--- Final Consolidated Reconciliation Data (first 5 rows) ---")
        if not final_recon_df.empty:
            print(final_recon_df.head())
            print(f"\nTotal rows in consolidated data: {len(final_recon_df)}")
            # Optional: Save the consolidated data to a new CSV file
            # final_recon_df.to_csv("consolidated_recon_output.csv", index=False)
            # print("\nConsolidated data saved to 'consolidated_recon_output.csv'")
        else:
            print("Consolidated DataFrame is empty. Check mappings and file paths.")
