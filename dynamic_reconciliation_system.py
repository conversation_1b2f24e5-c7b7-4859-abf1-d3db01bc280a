#!/usr/bin/env python3
"""
Dynamic Reconciliation System for Traders and Brokers
Handles 10+ trader files and 20+ broker files with flexible column mapping
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os
import hashlib
import json
from pathlib import Path
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import logging
from typing import Dict, List, Any, Optional

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DynamicReconciliationSystem:
    def __init__(self):
        # Enhanced standard columns based on your requirements
        self.STANDARD_RECON_COLUMNS = [
            "Date",
            "Account",
            "State", 
            "Contract",
            "Expiry",
            "Price",
            "Lot",
            "Multiplication Factor",
            "Contract Value",  # Calculated
            "Commission",
            "Input Source",
            "Venue",
            "Closing Price",
            "Closing Contract Value",  # Calculated
            "Profit/Loss",  # Calculated
            "Trade Type",
            "Quantity",
            "Trader Name",
            "Order ID",
            "Broker Code"
        ]
        
        # File tracking and configuration
        self.processed_files_db = "processed_files.json"
        self.mappings_db = "column_mappings.json"
        self.processed_files = self.load_processed_files()
        self.saved_mappings = self.load_saved_mappings()
        
        # State mapping for normalization
        self.STATE_MAPPINGS = {
            'mh': 'Maharashtra', 'maharashtra': 'Maharashtra', 'maha': 'Maharashtra',
            'dl': 'Delhi', 'delhi': 'Delhi', 'new delhi': 'Delhi',
            'gj': 'Gujarat', 'gujarat': 'Gujarat', 'guj': 'Gujarat',
            'ka': 'Karnataka', 'karnataka': 'Karnataka', 'kar': 'Karnataka',
            'tn': 'Tamil Nadu', 'tamil nadu': 'Tamil Nadu', 'tamilnadu': 'Tamil Nadu',
            'ap': 'Andhra Pradesh', 'andhra pradesh': 'Andhra Pradesh',
            'ts': 'Telangana', 'telangana': 'Telangana',
            'wb': 'West Bengal', 'west bengal': 'West Bengal',
            'up': 'Uttar Pradesh', 'uttar pradesh': 'Uttar Pradesh',
            'rj': 'Rajasthan', 'rajasthan': 'Rajasthan',
            'mp': 'Madhya Pradesh', 'madhya pradesh': 'Madhya Pradesh',
            'or': 'Odisha', 'odisha': 'Odisha', 'orissa': 'Odisha',
            'pb': 'Punjab', 'punjab': 'Punjab',
            'hr': 'Haryana', 'haryana': 'Haryana',
            'jh': 'Jharkhand', 'jharkhand': 'Jharkhand',
            'br': 'Bihar', 'bihar': 'Bihar',
            'as': 'Assam', 'assam': 'Assam',
            'kl': 'Kerala', 'kerala': 'Kerala',
            'ch': 'Chandigarh', 'chandigarh': 'Chandigarh'
        }
        
        # Account extraction patterns
        self.ACCOUNT_PATTERNS = {
            'direct': lambda x: str(x).strip(),
            'last_6_digits': lambda x: str(x)[-6:] if len(str(x)) >= 6 else str(x),
            'first_6_digits': lambda x: str(x)[:6] if len(str(x)) >= 6 else str(x),
            'last_5_digits': lambda x: str(x)[-5:] if len(str(x)) >= 5 else str(x),
            'numeric_only': lambda x: ''.join(filter(str.isdigit, str(x))),
            'alphanumeric_only': lambda x: ''.join(filter(str.isalnum, str(x)))
        }
    
    def load_processed_files(self) -> Dict:
        """Load the database of already processed files"""
        if os.path.exists(self.processed_files_db):
            try:
                with open(self.processed_files_db, 'r') as f:
                    return json.load(f)
            except:
                return {}
        return {}
    
    def save_processed_files(self):
        """Save the processed files database"""
        with open(self.processed_files_db, 'w') as f:
            json.dump(self.processed_files, f, indent=2)
    
    def load_saved_mappings(self) -> Dict:
        """Load saved column mappings"""
        if os.path.exists(self.mappings_db):
            try:
                with open(self.mappings_db, 'r') as f:
                    return json.load(f)
            except:
                return {}
        return {}
    
    def save_mappings(self, mappings: Dict):
        """Save column mappings for future use"""
        self.saved_mappings.update(mappings)
        with open(self.mappings_db, 'w') as f:
            json.dump(self.saved_mappings, f, indent=2)
    
    def get_file_hash(self, file_path: str) -> str:
        """Generate hash for file to detect duplicates"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    
    def is_file_processed(self, file_path: str) -> bool:
        """Check if file has already been processed"""
        file_hash = self.get_file_hash(file_path)
        file_name = os.path.basename(file_path)
        
        return (file_hash in self.processed_files.values() or 
                file_name in self.processed_files)
    
    def mark_file_processed(self, file_path: str):
        """Mark file as processed"""
        file_hash = self.get_file_hash(file_path)
        file_name = os.path.basename(file_path)
        self.processed_files[file_name] = file_hash
        self.save_processed_files()
    
    def normalize_date(self, date_str: Any) -> Optional[str]:
        """Enhanced date normalization with multiple formats"""
        if pd.isna(date_str) or date_str == '':
            return None
        
        try:
            if isinstance(date_str, datetime):
                return date_str.strftime('%Y-%m-%d')
            
            date_str = str(date_str).strip()
            
            # Common date formats
            date_formats = [
                '%Y-%m-%d', '%d/%m/%Y', '%m/%d/%Y', '%d-%m-%Y', '%m-%d-%Y',
                '%d/%m/%y', '%m/%d/%y', '%d-%m-%y', '%m-%d-%y',
                '%d-%b-%Y', '%d-%B-%Y', '%b-%d-%Y', '%B-%d-%Y',
                '%d %b %Y', '%d %B %Y', '%b %d %Y', '%B %d %Y',
                '%Y/%m/%d', '%d.%m.%Y', '%m.%d.%Y'
            ]
            
            for fmt in date_formats:
                try:
                    return datetime.strptime(date_str, fmt).strftime('%Y-%m-%d')
                except ValueError:
                    continue
            
            # Try pandas parsing as fallback
            return pd.to_datetime(date_str, dayfirst=True).strftime('%Y-%m-%d')
            
        except Exception as e:
            logger.warning(f"Could not normalize date '{date_str}': {e}")
            return None
    
    def normalize_state(self, state_str: Any) -> Optional[str]:
        """Normalize state names using mapping"""
        if pd.isna(state_str) or state_str == '':
            return None
        
        state_clean = str(state_str).lower().strip()
        return self.STATE_MAPPINGS.get(state_clean, str(state_str).title())
    
    def extract_account_number(self, account_value: Any, logic_type: str = "direct") -> Optional[str]:
        """Enhanced account number extraction with multiple patterns"""
        if pd.isna(account_value) or account_value == '':
            return None
        
        if logic_type in self.ACCOUNT_PATTERNS:
            return self.ACCOUNT_PATTERNS[logic_type](account_value)
        else:
            logger.warning(f"Unknown account extraction logic: {logic_type}")
            return str(account_value).strip()
    
    def calculate_contract_value(self, price: Any, lot: Any, contract: Any, mult_factor: Any) -> float:
        """Calculate Contract Value = Price × Lot × Contract × Multiplication Factor"""
        try:
            price = float(price) if pd.notna(price) and price != '' else 0
            lot = float(lot) if pd.notna(lot) and lot != '' else 1
            contract = float(contract) if pd.notna(contract) and contract != '' else 1
            mult_factor = float(mult_factor) if pd.notna(mult_factor) and mult_factor != '' else 1
            
            return price * lot * contract * mult_factor
        except (ValueError, TypeError):
            return 0.0
    
    def calculate_closing_contract_value(self, closing_price: Any, lot: Any, contract: Any, mult_factor: Any) -> float:
        """Calculate Closing Contract Value"""
        return self.calculate_contract_value(closing_price, lot, contract, mult_factor)
    
    def calculate_profit_loss(self, contract_value: Any, closing_contract_value: Any, commission: Any = 0) -> float:
        """Calculate Profit/Loss = Closing Contract Value - Contract Value - Commission"""
        try:
            cv = float(contract_value) if pd.notna(contract_value) else 0
            ccv = float(closing_contract_value) if pd.notna(closing_contract_value) else 0
            comm = float(commission) if pd.notna(commission) else 0

            return ccv - cv - comm
        except (ValueError, TypeError):
            return 0.0

    def load_file_with_detection(self, file_path: str) -> Optional[pd.DataFrame]:
        """Load file with automatic header detection"""
        try:
            # Try different skip rows to find the header
            for skip_rows in range(0, 10):
                try:
                    df = pd.read_csv(file_path, skiprows=skip_rows)
                    if len(df.columns) > 3 and not df.empty:  # Valid header found
                        logger.info(f"Loaded {file_path} with {skip_rows} rows skipped, {len(df)} records")
                        return df
                except:
                    continue

            # Try Excel format if CSV fails
            try:
                df = pd.read_excel(file_path)
                logger.info(f"Loaded Excel file {file_path} with {len(df)} records")
                return df
            except:
                pass

            logger.error(f"Could not load file: {file_path}")
            return None

        except Exception as e:
            logger.error(f"Error loading file {file_path}: {e}")
            return None

    def create_file_upload_ui(self) -> Dict[str, List[str]]:
        """Create UI for separate trader and broker file uploads"""
        root = tk.Tk()
        root.title("Dynamic Reconciliation System - File Upload")
        root.geometry("800x600")

        # Result storage
        result = {'trader_files': [], 'broker_files': [], 'cancelled': False}

        # Main frame
        main_frame = ttk.Frame(root, padding="20")
        main_frame.pack(fill='both', expand=True)

        # Title
        title_label = ttk.Label(main_frame, text="Dynamic Brokers vs Traders Reconciliation",
                               font=('Arial', 16, 'bold'))
        title_label.pack(pady=(0, 20))

        # Instructions
        instructions = ttk.Label(main_frame,
                                text="Upload trader and broker files separately. System supports 10+ trader and 20+ broker files.",
                                font=('Arial', 10))
        instructions.pack(pady=(0, 20))

        # Trader files section
        trader_frame = ttk.LabelFrame(main_frame, text="Trader Files", padding="10")
        trader_frame.pack(fill='x', pady=(0, 10))

        trader_listbox = tk.Listbox(trader_frame, height=6)
        trader_listbox.pack(fill='x', pady=(0, 10))

        trader_button_frame = ttk.Frame(trader_frame)
        trader_button_frame.pack(fill='x')

        def add_trader_files():
            files = filedialog.askopenfilenames(
                title="Select Trader Files",
                filetypes=[("CSV files", "*.csv"), ("Excel files", "*.xlsx"), ("All files", "*.*")]
            )
            for file in files:
                if file not in result['trader_files']:
                    result['trader_files'].append(file)
                    trader_listbox.insert(tk.END, os.path.basename(file))

        def remove_trader_file():
            selection = trader_listbox.curselection()
            if selection:
                index = selection[0]
                trader_listbox.delete(index)
                result['trader_files'].pop(index)

        ttk.Button(trader_button_frame, text="Add Trader Files", command=add_trader_files).pack(side='left', padx=(0, 5))
        ttk.Button(trader_button_frame, text="Remove Selected", command=remove_trader_file).pack(side='left')

        # Broker files section
        broker_frame = ttk.LabelFrame(main_frame, text="Broker Files", padding="10")
        broker_frame.pack(fill='x', pady=(0, 20))

        broker_listbox = tk.Listbox(broker_frame, height=6)
        broker_listbox.pack(fill='x', pady=(0, 10))

        broker_button_frame = ttk.Frame(broker_frame)
        broker_button_frame.pack(fill='x')

        def add_broker_files():
            files = filedialog.askopenfilenames(
                title="Select Broker Files",
                filetypes=[("CSV files", "*.csv"), ("Excel files", "*.xlsx"), ("All files", "*.*")]
            )
            for file in files:
                if file not in result['broker_files']:
                    result['broker_files'].append(file)
                    broker_listbox.insert(tk.END, os.path.basename(file))

        def remove_broker_file():
            selection = broker_listbox.curselection()
            if selection:
                index = selection[0]
                broker_listbox.delete(index)
                result['broker_files'].pop(index)

        ttk.Button(broker_button_frame, text="Add Broker Files", command=add_broker_files).pack(side='left', padx=(0, 5))
        ttk.Button(broker_button_frame, text="Remove Selected", command=remove_broker_file).pack(side='left')

        # Control buttons
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill='x')

        def proceed():
            if not result['trader_files'] and not result['broker_files']:
                messagebox.showwarning("No Files", "Please select at least one trader or broker file.")
                return
            root.quit()

        def cancel():
            result['cancelled'] = True
            root.quit()

        ttk.Button(control_frame, text="Proceed to Mapping", command=proceed).pack(side='right', padx=(5, 0))
        ttk.Button(control_frame, text="Cancel", command=cancel).pack(side='right')

        # Status
        status_label = ttk.Label(main_frame, text="Select files and click 'Proceed to Mapping'")
        status_label.pack(pady=(10, 0))

        root.mainloop()
        root.destroy()

        return result

    def create_dynamic_mapping_ui(self, all_files_data: Dict[str, pd.DataFrame]) -> Optional[Dict]:
        """Create dynamic column mapping interface with dropdowns for all available columns"""
        root = tk.Tk()
        root.title("Dynamic Column Mapping")
        root.geometry("1000x700")

        # Create scrollable frame
        canvas = tk.Canvas(root)
        scrollbar = ttk.Scrollbar(root, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Main content frame
        main_frame = ttk.Frame(scrollable_frame, padding="20")
        main_frame.pack(fill='both', expand=True)

        # Title
        title_label = ttk.Label(main_frame, text="Dynamic Column Mapping",
                               font=('Arial', 16, 'bold'))
        title_label.pack(pady=(0, 20))

        # Instructions
        instructions = ttk.Label(main_frame,
                                text="Map each reconciliation column to source columns from your uploaded files.\n" +
                                     "Select the appropriate transformation logic for Account and State fields.",
                                font=('Arial', 10))
        instructions.pack(pady=(0, 20))

        # Collect all available columns from all files
        all_columns = set()
        for file_name, df in all_files_data.items():
            all_columns.update(df.columns.tolist())

        all_columns = sorted(list(all_columns))
        column_options = [""] + all_columns  # Empty option first

        # Storage for mappings
        mappings = {}
        logic_vars = {}

        # Create mapping interface for each standard column
        mapping_frame = ttk.Frame(main_frame)
        mapping_frame.pack(fill='both', expand=True)

        # Headers
        ttk.Label(mapping_frame, text="Reconciliation Column", font=('Arial', 11, 'bold')).grid(
            row=0, column=0, padx=10, pady=5, sticky='w')
        ttk.Label(mapping_frame, text="Source Column", font=('Arial', 11, 'bold')).grid(
            row=0, column=1, padx=10, pady=5, sticky='w')
        ttk.Label(mapping_frame, text="Transformation Logic", font=('Arial', 11, 'bold')).grid(
            row=0, column=2, padx=10, pady=5, sticky='w')

        row = 1
        for standard_col in self.STANDARD_RECON_COLUMNS:
            # Skip calculated columns
            if standard_col in ["Contract Value", "Closing Contract Value", "Profit/Loss"]:
                continue

            # Standard column label
            ttk.Label(mapping_frame, text=standard_col).grid(
                row=row, column=0, padx=10, pady=2, sticky='w')

            # Source column dropdown
            var = tk.StringVar()
            dropdown = ttk.Combobox(mapping_frame, textvariable=var, values=column_options, width=30)
            dropdown.grid(row=row, column=1, padx=10, pady=2, sticky='w')
            mappings[standard_col] = var

            # Logic dropdown for specific fields
            logic_var = tk.StringVar()
            if standard_col == "Account":
                logic_options = ["direct", "last_6_digits", "first_6_digits", "last_5_digits",
                               "numeric_only", "alphanumeric_only"]
                logic_dropdown = ttk.Combobox(mapping_frame, textvariable=logic_var,
                                            values=logic_options, width=20)
                logic_dropdown.set("direct")
                logic_dropdown.grid(row=row, column=2, padx=10, pady=2, sticky='w')
                logic_vars[standard_col] = logic_var
            elif standard_col == "State":
                logic_var.set("normalize")
                ttk.Label(mapping_frame, text="Auto-normalize").grid(
                    row=row, column=2, padx=10, pady=2, sticky='w')
                logic_vars[standard_col] = logic_var
            elif standard_col == "Date":
                logic_var.set("normalize")
                ttk.Label(mapping_frame, text="Auto-normalize").grid(
                    row=row, column=2, padx=10, pady=2, sticky='w')
                logic_vars[standard_col] = logic_var
            else:
                logic_var.set("direct")
                ttk.Label(mapping_frame, text="Direct copy").grid(
                    row=row, column=2, padx=10, pady=2, sticky='w')
                logic_vars[standard_col] = logic_var

            row += 1

        # Buttons frame
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill='x', pady=(20, 0))

        result = {'mappings': None, 'logic': None, 'cancelled': False}

        def save_mappings():
            final_mappings = {}
            final_logic = {}

            for standard_col, var in mappings.items():
                if var.get():  # Only save non-empty mappings
                    final_mappings[standard_col] = var.get()
                    if standard_col in logic_vars:
                        final_logic[standard_col] = logic_vars[standard_col].get()

            if not final_mappings:
                messagebox.showwarning("No Mappings", "Please select at least one column mapping.")
                return

            result['mappings'] = final_mappings
            result['logic'] = final_logic
            root.quit()

        def cancel():
            result['cancelled'] = True
            root.quit()

        ttk.Button(button_frame, text="Apply Mappings", command=save_mappings).pack(side='right', padx=(5, 0))
        ttk.Button(button_frame, text="Cancel", command=cancel).pack(side='right')

        # Pack canvas and scrollbar
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        root.mainloop()
        root.destroy()

        if result['cancelled']:
            return None

        return result

    def apply_dynamic_reconciliation(self, all_files_data: Dict[str, pd.DataFrame],
                                   mappings: Dict[str, str], logic: Dict[str, str]) -> pd.DataFrame:
        """Apply reconciliation with dynamic mappings and business logic"""
        consolidated_records = []

        for file_name, df in all_files_data.items():
            logger.info(f"Processing {file_name} with {len(df)} records")

            # Determine file type based on name or content
            file_type = "Trader" if "trader" in file_name.lower() else "Broker"

            for _, row in df.iterrows():
                record = {}

                # Apply mappings with transformations
                for standard_col, source_col in mappings.items():
                    if source_col in df.columns:
                        raw_value = row[source_col]

                        # Apply transformation logic
                        if standard_col == "Date":
                            record[standard_col] = self.normalize_date(raw_value)
                        elif standard_col == "State":
                            record[standard_col] = self.normalize_state(raw_value)
                        elif standard_col == "Account":
                            account_logic = logic.get(standard_col, "direct")
                            record[standard_col] = self.extract_account_number(raw_value, account_logic)
                        else:
                            # Direct copy with basic cleaning
                            if pd.notna(raw_value) and raw_value != '':
                                record[standard_col] = str(raw_value).strip()
                            else:
                                record[standard_col] = None
                    else:
                        record[standard_col] = None

                # Add metadata
                record['Input Source'] = file_name
                record['File Type'] = file_type

                # Calculate business logic fields
                record['Contract Value'] = self.calculate_contract_value(
                    record.get('Price'),
                    record.get('Lot'),
                    record.get('Contract'),
                    record.get('Multiplication Factor')
                )

                record['Closing Contract Value'] = self.calculate_closing_contract_value(
                    record.get('Closing Price'),
                    record.get('Lot'),
                    record.get('Contract'),
                    record.get('Multiplication Factor')
                )

                record['Profit/Loss'] = self.calculate_profit_loss(
                    record['Contract Value'],
                    record['Closing Contract Value'],
                    record.get('Commission', 0)
                )

                consolidated_records.append(record)

        # Create consolidated DataFrame
        consolidated_df = pd.DataFrame(consolidated_records)

        # Ensure all standard columns exist
        for col in self.STANDARD_RECON_COLUMNS:
            if col not in consolidated_df.columns:
                consolidated_df[col] = None

        # Reorder columns
        consolidated_df = consolidated_df[self.STANDARD_RECON_COLUMNS + ['Input Source', 'File Type']]

        logger.info(f"Consolidated {len(consolidated_df)} total records")
        return consolidated_df

    def save_console_sheet(self, consolidated_df: pd.DataFrame, output_path: str = None) -> bool:
        """Save the final console sheet with enhanced formatting"""
        if output_path is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_path = f"Dynamic_Console_Reconciliation_{timestamp}.xlsx"

        try:
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                # Main console sheet
                consolidated_df.to_excel(writer, sheet_name='Console', index=False)

                # Summary sheet
                summary_data = []

                # Summary by file type
                file_type_summary = consolidated_df.groupby('File Type').agg({
                    'Date': 'count',
                    'Contract Value': 'sum',
                    'Commission': 'sum',
                    'Profit/Loss': 'sum'
                }).round(2)
                file_type_summary.columns = ['Record Count', 'Total Contract Value', 'Total Commission', 'Total P&L']

                # Summary by input source
                source_summary = consolidated_df.groupby('Input Source').agg({
                    'Date': 'count',
                    'Contract Value': 'sum',
                    'Commission': 'sum',
                    'Profit/Loss': 'sum'
                }).round(2)
                source_summary.columns = ['Record Count', 'Total Contract Value', 'Total Commission', 'Total P&L']

                # Write summaries
                file_type_summary.to_excel(writer, sheet_name='Summary_by_Type', startrow=0)
                source_summary.to_excel(writer, sheet_name='Summary_by_Source', startrow=0)

                # Auto-adjust column widths for all sheets
                for sheet_name in writer.sheets:
                    worksheet = writer.sheets[sheet_name]
                    for column in worksheet.columns:
                        max_length = 0
                        column_letter = column[0].column_letter
                        for cell in column:
                            try:
                                if len(str(cell.value)) > max_length:
                                    max_length = len(str(cell.value))
                            except:
                                pass
                        adjusted_width = min(max_length + 2, 50)
                        worksheet.column_dimensions[column_letter].width = adjusted_width

            logger.info(f"Console sheet saved to {output_path}")
            return True

        except Exception as e:
            logger.error(f"Error saving console sheet: {e}")
            return False

    def run_dynamic_reconciliation(self) -> Optional[pd.DataFrame]:
        """Main execution function for dynamic reconciliation"""
        try:
            # Step 1: File Upload
            print("🚀 Starting Dynamic Reconciliation System")
            print("=" * 60)

            file_selection = self.create_file_upload_ui()
            if file_selection['cancelled']:
                print("❌ Process cancelled by user")
                return None

            all_files = file_selection['trader_files'] + file_selection['broker_files']
            if not all_files:
                print("❌ No files selected")
                return None

            print(f"📁 Selected {len(file_selection['trader_files'])} trader files and {len(file_selection['broker_files'])} broker files")

            # Step 2: Load all files
            print("\n📖 Loading files...")
            all_files_data = {}

            for file_path in all_files:
                if self.is_file_processed(file_path):
                    print(f"⏭️  Skipping already processed file: {os.path.basename(file_path)}")
                    continue

                df = self.load_file_with_detection(file_path)
                if df is not None:
                    file_name = os.path.basename(file_path)
                    all_files_data[file_name] = df
                    print(f"✅ Loaded {file_name}: {len(df)} records, {len(df.columns)} columns")

            if not all_files_data:
                print("❌ No files could be loaded")
                return None

            # Step 3: Dynamic Column Mapping
            print(f"\n🔗 Creating dynamic column mappings...")
            print(f"Available columns across all files: {sum(len(df.columns) for df in all_files_data.values())} total")

            mapping_result = self.create_dynamic_mapping_ui(all_files_data)
            if mapping_result is None:
                print("❌ Mapping cancelled by user")
                return None

            mappings = mapping_result['mappings']
            logic = mapping_result['logic']

            print(f"✅ Mapped {len(mappings)} columns with transformation logic")

            # Save mappings for future use
            self.save_mappings({
                'mappings': mappings,
                'logic': logic,
                'timestamp': datetime.now().isoformat()
            })

            # Step 4: Apply Reconciliation
            print(f"\n⚙️  Applying reconciliation logic...")
            consolidated_df = self.apply_dynamic_reconciliation(all_files_data, mappings, logic)

            # Step 5: Mark files as processed
            for file_path in all_files:
                self.mark_file_processed(file_path)

            print(f"✅ Reconciliation completed successfully!")
            print(f"📊 Total records processed: {len(consolidated_df)}")

            # Show summary statistics
            if not consolidated_df.empty:
                print(f"\n📈 Summary Statistics:")
                print(f"   Total Contract Value: ₹{consolidated_df['Contract Value'].sum():,.2f}")
                print(f"   Total Commission: ₹{consolidated_df['Commission'].sum():,.2f}")
                print(f"   Total P&L: ₹{consolidated_df['Profit/Loss'].sum():,.2f}")

                print(f"\n📋 Records by File Type:")
                type_counts = consolidated_df['File Type'].value_counts()
                for file_type, count in type_counts.items():
                    print(f"   {file_type}: {count} records")

            return consolidated_df

        except Exception as e:
            logger.error(f"Error in dynamic reconciliation: {e}")
            print(f"❌ An error occurred: {e}")
            return None

def main():
    """Main execution function"""
    try:
        # Initialize system
        recon_system = DynamicReconciliationSystem()

        # Run reconciliation
        consolidated_df = recon_system.run_dynamic_reconciliation()

        if consolidated_df is not None:
            # Save results
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = f"Dynamic_Console_Reconciliation_{timestamp}.xlsx"

            success = recon_system.save_console_sheet(consolidated_df, output_file)

            if success:
                print(f"\n💾 Results saved to: {output_file}")
                print(f"📁 File includes Console sheet and Summary sheets")

                # Show final summary
                print(f"\n🎯 Final Results:")
                print(f"   📊 Total Records: {len(consolidated_df)}")
                print(f"   💰 Total Contract Value: ₹{consolidated_df['Contract Value'].sum():,.2f}")
                print(f"   📈 Net P&L: ₹{consolidated_df['Profit/Loss'].sum():,.2f}")

            else:
                print(f"❌ Failed to save results")
        else:
            print(f"❌ Reconciliation failed")

    except Exception as e:
        logger.error(f"Unexpected error in main: {e}")
        print(f"❌ An unexpected error occurred: {e}")

if __name__ == "__main__":
    main()
