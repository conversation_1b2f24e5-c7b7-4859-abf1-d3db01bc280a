# 🚀 Brokers vs Traders Reconciliation Automation System

A comprehensive Python-based solution for automating the reconciliation of trading data from multiple broker and trader files into a unified console sheet.

## 📋 Features

### ✅ Core Functionality
- **Dynamic Column Mapping**: Intelligent detection and user-configurable mapping of columns across different file formats
- **Business Logic Calculations**: Automated calculation of Contract Value, Profit/Loss, and other financial metrics
- **Duplicate Prevention**: Hash-based file tracking to prevent reprocessing of already uploaded files
- **Multi-format Support**: Handles CSV and Excel files with varying structures
- **Scalable Architecture**: Designed to handle 1-4 files initially, scalable to 20+ files

### 🧮 Automated Calculations
- **Contract Value** = Price × Lot × Contract × Multiplication Factor
- **Profit/Loss** = Closing Contract Value - Contract Value - Commission
- **Account Number Extraction** with configurable logic (direct, last N digits, etc.)
- **Date Normalization** supporting multiple date formats

### 🎯 Key Improvements Over Original Script
1. **GUI Interface**: User-friendly file selection and column mapping
2. **Automatic Column Detection**: Smart pattern matching for common trading columns
3. **File Tracking**: Prevents duplicate processing
4. **Enhanced Error Handling**: Comprehensive logging and error management
5. **Business Logic Integration**: Built-in financial calculations
6. **Persistent Mappings**: Save and reuse column mappings

## 🛠️ Installation

### Prerequisites
- Python 3.7 or higher
- pip package manager

### Setup
1. **Clone or download the project files**
2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

### Dependencies
- `pandas>=1.5.0` - Data manipulation and analysis
- `openpyxl>=3.0.0` - Excel file handling
- `numpy>=1.21.0` - Numerical computations
- `python-docx>=0.8.11` - Word document processing

## 🚀 Quick Start

### 1. Create Sample Data (Optional)
```bash
python test_reconciliation.py
```
This creates sample CSV files to test the system.

### 2. Run the Reconciliation System
```bash
python enhanced_reconciliation_system.py
```

### 3. Follow the GUI Workflow
1. **File Selection**: Choose your trader/broker files
2. **Column Mapping**: Map source columns to standard reconciliation columns
3. **Processing**: System applies business logic and calculations
4. **Output**: Generates consolidated Excel file with results

## 📊 Standard Reconciliation Columns

The system standardizes data into these columns:

| Column | Description |
|--------|-------------|
| Date | Normalized transaction date |
| Account Number | Standardized account identifier |
| State | Standardized state/region |
| Contract | Contract/instrument identifier |
| Expiry | Contract expiry date |
| Instrument/Symbol | Trading symbol |
| Trade Type | Buy/Sell direction |
| Quantity/Size | Trade quantity |
| Lot | Lot size |
| Price | Trade price |
| Multiplication Factor | Contract multiplier |
| **Contract Value** | *Calculated: Price × Lot × Contract × Multiplier* |
| Value/Notional | Notional value |
| Commission | Trading fees |
| Input Source | Source file name |
| Venue | Trading venue/exchange |
| Closing Price | Closing price |
| Closing Contract Value | *Calculated closing value* |
| **Profit/Loss** | *Calculated P&L* |
| Broker ID/Code | Broker identifier |
| Trader Name/ID | Trader identifier |
| Order ID/Trade ID | Order reference |

## 🔧 Configuration

### Column Mapping
The system automatically detects common column patterns but allows manual override:

```python
# Example auto-detection patterns
mapping_patterns = {
    "Date": ["date", "trade date", "transaction date"],
    "Account Number": ["account", "acc", "ucc", "client"],
    "Price": ["price", "rate", "px"],
    # ... more patterns
}
```

### Business Logic Customization
Modify calculation logic in the `apply_business_calculations` method:

```python
def apply_business_calculations(self, recon_record, source_row, mappings):
    # Contract Value = Price × Lot × Contract × Multiplication Factor
    contract_value = self.calculate_contract_value(price, lot, contract, mult_factor)
    
    # Custom business logic here
    # ...
```

## 📁 File Structure

```
RECCON/
├── enhanced_reconciliation_system.py  # Main reconciliation system
├── reconciliation_system.py          # Original system (reference)
├── test_reconciliation.py            # Sample data generator
├── read_docx.py                      # Document reader utility
├── requirements.txt                  # Python dependencies
├── README.md                         # This file
├── processed_files.json             # File tracking database (auto-generated)
├── column_mappings.json             # Saved mappings (auto-generated)
└── Console_Reconciliation_*.xlsx    # Output files (auto-generated)
```

## 🔍 Usage Examples

### Processing Multiple Files
1. Run the system: `python enhanced_reconciliation_system.py`
2. Select multiple CSV/Excel files
3. Map columns for each file type
4. Review generated console sheet

### Handling Different Formats
The system handles various column naming conventions:
- **Date**: "Trade Date", "Transaction Date", "Date", "Dt"
- **Account**: "UCC Code", "Account", "Client Code", "Account Number"
- **Price**: "Price", "Rate", "Px"

## 🚨 Error Handling

The system includes comprehensive error handling:
- **File Loading Errors**: Automatic header detection with fallback
- **Data Type Errors**: Graceful handling of invalid numeric values
- **Missing Mappings**: Clear warnings for unmapped columns
- **Calculation Errors**: Safe mathematical operations with defaults

## 📈 Performance & Scalability

- **Memory Efficient**: Processes files incrementally
- **Scalable**: Designed for 20+ files
- **Fast Processing**: Optimized pandas operations
- **Duplicate Prevention**: Hash-based file tracking

## 🔮 Future Enhancements

- **Web Interface**: Flask/Django web application
- **Database Integration**: PostgreSQL/MySQL support
- **Advanced Analytics**: Built-in reporting and visualization
- **API Integration**: Direct broker API connections
- **Real-time Processing**: Live data reconciliation

## 🐛 Troubleshooting

### Common Issues
1. **Import Errors**: Ensure all dependencies are installed
2. **File Format Issues**: Check CSV encoding and delimiters
3. **Column Mapping**: Verify source column names match exactly
4. **Calculation Errors**: Check for missing required fields

### Logging
The system provides detailed logging. Check console output for:
- File loading status
- Column mapping results
- Calculation warnings
- Processing statistics

## 📞 Support

For issues or questions:
1. Check the error logs for detailed information
2. Verify file formats and column names
3. Ensure all required dependencies are installed
4. Review the sample data format in `test_reconciliation.py`

---

**Built with ❤️ for automated financial reconciliation**
