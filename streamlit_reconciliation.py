#!/usr/bin/env python3
"""
Streamlit Dynamic Reconciliation System for Traders and Brokers
Web-based interface for handling 10+ trader files and 20+ broker files
"""

import streamlit as st
import pandas as pd
import numpy as np
from datetime import datetime
import os
import hashlib
import json
from io import BytesIO
import logging
from typing import Dict, List, Any, Optional
import pickle

# Configure page
st.set_page_config(
    page_title="Dynamic Reconciliation System",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class StreamlitReconciliationSystem:
    def __init__(self):
        # Enhanced standard columns based on requirements
        self.STANDARD_RECON_COLUMNS = [
            "Date", "Account", "State", "Contract", "Expiry", "Price", "Lot",
            "Multiplication Factor", "Contract Value", "Commission", "Input Source",
            "Venue", "Closing Price", "Closing Contract Value", "Profit/Loss",
            "Trade Type", "Quantity", "Trader Name", "Order ID", "Broker Code"
        ]

        # Mapping profiles storage
        self.profiles_file = "mapping_profiles.json"
        self.load_mapping_profiles()
        
        # State mapping for normalization
        self.STATE_MAPPINGS = {
            'mh': 'Maharashtra', 'maharashtra': 'Maharashtra', 'maha': 'Maharashtra',
            'dl': 'Delhi', 'delhi': 'Delhi', 'new delhi': 'Delhi',
            'gj': 'Gujarat', 'gujarat': 'Gujarat', 'guj': 'Gujarat',
            'ka': 'Karnataka', 'karnataka': 'Karnataka', 'kar': 'Karnataka',
            'tn': 'Tamil Nadu', 'tamil nadu': 'Tamil Nadu', 'tamilnadu': 'Tamil Nadu',
            'ap': 'Andhra Pradesh', 'andhra pradesh': 'Andhra Pradesh',
            'ts': 'Telangana', 'telangana': 'Telangana',
            'wb': 'West Bengal', 'west bengal': 'West Bengal',
            'up': 'Uttar Pradesh', 'uttar pradesh': 'Uttar Pradesh',
            'rj': 'Rajasthan', 'rajasthan': 'Rajasthan',
            'mp': 'Madhya Pradesh', 'madhya pradesh': 'Madhya Pradesh',
            'or': 'Odisha', 'odisha': 'Odisha', 'orissa': 'Odisha',
            'pb': 'Punjab', 'punjab': 'Punjab',
            'hr': 'Haryana', 'haryana': 'Haryana',
            'jh': 'Jharkhand', 'jharkhand': 'Jharkhand',
            'br': 'Bihar', 'bihar': 'Bihar',
            'as': 'Assam', 'assam': 'Assam',
            'kl': 'Kerala', 'kerala': 'Kerala',
            'ch': 'Chandigarh', 'chandigarh': 'Chandigarh'
        }
        
        # Account extraction patterns
        self.ACCOUNT_PATTERNS = {
            'direct': lambda x: str(x).strip(),
            'last_6_digits': lambda x: str(x)[-6:] if len(str(x)) >= 6 else str(x),
            'first_6_digits': lambda x: str(x)[:6] if len(str(x)) >= 6 else str(x),
            'last_5_digits': lambda x: str(x)[-5:] if len(str(x)) >= 5 else str(x),
            'numeric_only': lambda x: ''.join(filter(str.isdigit, str(x))),
            'alphanumeric_only': lambda x: ''.join(filter(str.isalnum, str(x)))
        }

    def load_mapping_profiles(self):
        """Load saved mapping profiles from file"""
        try:
            if os.path.exists(self.profiles_file):
                with open(self.profiles_file, 'r') as f:
                    self.mapping_profiles = json.load(f)
            else:
                self.mapping_profiles = {}
        except Exception as e:
            logger.error(f"Error loading mapping profiles: {e}")
            self.mapping_profiles = {}

    def save_mapping_profiles(self):
        """Save mapping profiles to file"""
        try:
            with open(self.profiles_file, 'w') as f:
                json.dump(self.mapping_profiles, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving mapping profiles: {e}")

    def create_mapping_profile(self, profile_name: str, mappings: Dict[str, str],
                              logic: Dict[str, str], file_patterns: List[str] = None):
        """Create and save a new mapping profile"""
        profile = {
            'name': profile_name,
            'mappings': mappings,
            'logic': logic,
            'file_patterns': file_patterns or [],
            'created_date': datetime.now().isoformat(),
            'last_used': datetime.now().isoformat()
        }

        self.mapping_profiles[profile_name] = profile
        self.save_mapping_profiles()
        return profile

    def get_suggested_profile(self, file_names: List[str]) -> Optional[str]:
        """Suggest a mapping profile based on file names"""
        for profile_name, profile in self.mapping_profiles.items():
            file_patterns = profile.get('file_patterns', [])
            for pattern in file_patterns:
                for file_name in file_names:
                    if pattern.lower() in file_name.lower():
                        return profile_name
        return None

    def apply_mapping_profile(self, profile_name: str) -> tuple:
        """Apply a saved mapping profile"""
        if profile_name in self.mapping_profiles:
            profile = self.mapping_profiles[profile_name]
            # Update last used timestamp
            profile['last_used'] = datetime.now().isoformat()
            self.save_mapping_profiles()
            return profile['mappings'], profile['logic']
        return {}, {}

    def detect_file_type_from_content(self, df: pd.DataFrame, file_name: str) -> str:
        """Intelligently detect if file is trader or broker based on content and name"""
        file_name_lower = file_name.lower()

        # Check file name patterns
        if any(keyword in file_name_lower for keyword in ['trader', 'trade', 'cqg', 'trading']):
            return "Trader"
        elif any(keyword in file_name_lower for keyword in ['broker', 'brokerage', 'clearing']):
            return "Broker"

        # Check column patterns
        columns_lower = [col.lower() for col in df.columns]
        trader_indicators = ['trader', 'client', 'customer', 'account']
        broker_indicators = ['broker', 'clearing', 'settlement', 'exchange']

        trader_score = sum(1 for indicator in trader_indicators
                          if any(indicator in col for col in columns_lower))
        broker_score = sum(1 for indicator in broker_indicators
                          if any(indicator in col for col in columns_lower))

        return "Trader" if trader_score >= broker_score else "Broker"
    
    def normalize_date(self, date_str: Any) -> Optional[str]:
        """Enhanced date normalization with multiple formats"""
        if pd.isna(date_str) or date_str == '':
            return None
        
        try:
            if isinstance(date_str, datetime):
                return date_str.strftime('%Y-%m-%d')
            
            date_str = str(date_str).strip()
            
            # Common date formats
            date_formats = [
                '%Y-%m-%d', '%d/%m/%Y', '%m/%d/%Y', '%d-%m-%Y', '%m-%d-%Y',
                '%d/%m/%y', '%m/%d/%y', '%d-%m-%y', '%m-%d-%y',
                '%d-%b-%Y', '%d-%B-%Y', '%b-%d-%Y', '%B-%d-%Y',
                '%d %b %Y', '%d %B %Y', '%b %d %Y', '%B %d %Y',
                '%Y/%m/%d', '%d.%m.%Y', '%m.%d.%Y'
            ]
            
            for fmt in date_formats:
                try:
                    return datetime.strptime(date_str, fmt).strftime('%Y-%m-%d')
                except ValueError:
                    continue
            
            # Try pandas parsing as fallback
            return pd.to_datetime(date_str, dayfirst=True).strftime('%Y-%m-%d')
            
        except Exception as e:
            logger.warning(f"Could not normalize date '{date_str}': {e}")
            return None
    
    def normalize_state(self, state_str: Any) -> Optional[str]:
        """Normalize state names using mapping"""
        if pd.isna(state_str) or state_str == '':
            return None
        
        state_clean = str(state_str).lower().strip()
        return self.STATE_MAPPINGS.get(state_clean, str(state_str).title())
    
    def extract_account_number(self, account_value: Any, logic_type: str = "direct") -> Optional[str]:
        """Enhanced account number extraction with multiple patterns"""
        if pd.isna(account_value) or account_value == '':
            return None
        
        if logic_type in self.ACCOUNT_PATTERNS:
            return self.ACCOUNT_PATTERNS[logic_type](account_value)
        else:
            return str(account_value).strip()
    
    def calculate_contract_value(self, price: Any, lot: Any, contract: Any, mult_factor: Any) -> float:
        """Calculate Contract Value = Price × Lot × Contract × Multiplication Factor"""
        try:
            price = float(price) if pd.notna(price) and price != '' else 0
            lot = float(lot) if pd.notna(lot) and lot != '' else 1
            contract = float(contract) if pd.notna(contract) and contract != '' else 1
            mult_factor = float(mult_factor) if pd.notna(mult_factor) and mult_factor != '' else 1
            
            return price * lot * contract * mult_factor
        except (ValueError, TypeError):
            return 0.0
    
    def calculate_closing_contract_value(self, closing_price: Any, lot: Any, contract: Any, mult_factor: Any) -> float:
        """Calculate Closing Contract Value"""
        return self.calculate_contract_value(closing_price, lot, contract, mult_factor)
    
    def calculate_profit_loss(self, contract_value: Any, closing_contract_value: Any, commission: Any = 0) -> float:
        """Calculate Profit/Loss = Closing Contract Value - Contract Value - Commission"""
        try:
            cv = float(contract_value) if pd.notna(contract_value) else 0
            ccv = float(closing_contract_value) if pd.notna(closing_contract_value) else 0
            comm = float(commission) if pd.notna(commission) else 0
            
            return ccv - cv - comm
        except (ValueError, TypeError):
            return 0.0
    
    def load_file_with_detection(self, uploaded_file) -> Optional[pd.DataFrame]:
        """Load uploaded file with automatic header detection"""
        try:
            # Try CSV first
            if uploaded_file.name.endswith('.csv'):
                # Try different skip rows to find the header
                for skip_rows in range(0, 10):
                    try:
                        uploaded_file.seek(0)  # Reset file pointer
                        df = pd.read_csv(uploaded_file, skiprows=skip_rows)
                        if len(df.columns) > 3 and not df.empty:
                            return df
                    except:
                        continue
            
            # Try Excel format
            elif uploaded_file.name.endswith(('.xlsx', '.xls')):
                uploaded_file.seek(0)
                df = pd.read_excel(uploaded_file)
                return df
            
            return None
            
        except Exception as e:
            st.error(f"Error loading file {uploaded_file.name}: {e}")
            return None
    
    def apply_reconciliation(self, all_files_data: Dict[str, pd.DataFrame], 
                           mappings: Dict[str, str], logic: Dict[str, str]) -> pd.DataFrame:
        """Apply reconciliation with dynamic mappings and business logic"""
        consolidated_records = []
        
        for file_name, df in all_files_data.items():
            # Determine file type based on session state
            file_type = "Trader" if file_name in st.session_state.get('trader_file_names', []) else "Broker"
            
            for _, row in df.iterrows():
                record = {}
                
                # Apply mappings with transformations
                for standard_col, source_col in mappings.items():
                    if source_col and source_col in df.columns:
                        raw_value = row[source_col]
                        
                        # Apply transformation logic
                        if standard_col == "Date":
                            record[standard_col] = self.normalize_date(raw_value)
                        elif standard_col == "State":
                            record[standard_col] = self.normalize_state(raw_value)
                        elif standard_col == "Account":
                            account_logic = logic.get(standard_col, "direct")
                            record[standard_col] = self.extract_account_number(raw_value, account_logic)
                        else:
                            # Direct copy with basic cleaning
                            if pd.notna(raw_value) and raw_value != '':
                                record[standard_col] = str(raw_value).strip()
                            else:
                                record[standard_col] = None
                    else:
                        record[standard_col] = None
                
                # Add metadata
                record['Input Source'] = file_name
                record['File Type'] = file_type
                
                # Calculate business logic fields
                record['Contract Value'] = self.calculate_contract_value(
                    record.get('Price'), record.get('Lot'), 
                    record.get('Contract'), record.get('Multiplication Factor')
                )
                
                record['Closing Contract Value'] = self.calculate_closing_contract_value(
                    record.get('Closing Price'), record.get('Lot'), 
                    record.get('Contract'), record.get('Multiplication Factor')
                )
                
                record['Profit/Loss'] = self.calculate_profit_loss(
                    record['Contract Value'], record['Closing Contract Value'],
                    record.get('Commission', 0)
                )
                
                consolidated_records.append(record)
        
        # Create consolidated DataFrame
        consolidated_df = pd.DataFrame(consolidated_records)
        
        # Ensure all standard columns exist
        for col in self.STANDARD_RECON_COLUMNS:
            if col not in consolidated_df.columns:
                consolidated_df[col] = None
        
        # Reorder columns
        consolidated_df = consolidated_df[self.STANDARD_RECON_COLUMNS + ['Input Source', 'File Type']]
        
        return consolidated_df

# Initialize the system
@st.cache_resource
def get_recon_system():
    return StreamlitReconciliationSystem()

def main():
    # Initialize system
    recon_system = get_recon_system()

    # App header
    st.title("🚀 Hybrid Dynamic Reconciliation System")
    st.markdown("### Intelligent Mapping + Backend Logic for Brokers vs Traders")

    # Add info about the hybrid approach
    with st.expander("🎯 About the Hybrid Approach"):
        st.markdown("""
        **This system combines the best of both worlds:**

        1. **User-Driven Mapping**: Handle varied column names (Date vs Trade Date vs Expiry Date)
        2. **Intelligent Backend Logic**: Apply complex transformations (MH → Maharashtra, last 6 digits)
        3. **Reusable Profiles**: Save mappings for recurring file types (Star AGL013, CQG Trader, etc.)
        4. **One-Click Automation**: After initial setup, process similar files with a single click

        **Perfect for**: Large numbers of files from known brokers/traders with consistent formats
        """)

    # Initialize session state
    if 'all_files_data' not in st.session_state:
        st.session_state.all_files_data = {}
    if 'trader_file_names' not in st.session_state:
        st.session_state.trader_file_names = []
    if 'broker_file_names' not in st.session_state:
        st.session_state.broker_file_names = []
    if 'current_mappings' not in st.session_state:
        st.session_state.current_mappings = {}
    if 'current_logic' not in st.session_state:
        st.session_state.current_logic = {}
    
    # Sidebar for file uploads and profiles
    with st.sidebar:
        st.header("📁 File Upload & Profiles")

        # Mapping Profiles Section
        st.subheader("🎯 Mapping Profiles")

        # Show existing profiles
        if recon_system.mapping_profiles:
            st.write("**Saved Profiles:**")
            for profile_name, profile in recon_system.mapping_profiles.items():
                col1, col2 = st.columns([3, 1])
                with col1:
                    st.write(f"📋 {profile_name}")
                    st.caption(f"Last used: {profile['last_used'][:10]}")
                with col2:
                    if st.button("🗑️", key=f"delete_{profile_name}", help="Delete profile"):
                        del recon_system.mapping_profiles[profile_name]
                        recon_system.save_mapping_profiles()
                        st.rerun()
        else:
            st.info("No saved profiles yet. Create one after mapping columns!")

        st.markdown("---")

        # Trader files upload
        st.subheader("🔵 Trader Files")
        trader_files = st.file_uploader(
            "Upload Trader Files (10+ supported)",
            type=['csv', 'xlsx', 'xls'],
            accept_multiple_files=True,
            key="trader_files",
            help="Upload files from trading platforms (CQG, Star, etc.)"
        )

        # Broker files upload
        st.subheader("🔴 Broker Files")
        broker_files = st.file_uploader(
            "Upload Broker Files (20+ supported)",
            type=['csv', 'xlsx', 'xls'],
            accept_multiple_files=True,
            key="broker_files",
            help="Upload files from brokers and clearing houses"
        )
        
        # Process uploaded files
        if st.button("🔄 Load Files", type="primary"):
            with st.spinner("Loading and analyzing files..."):
                st.session_state.all_files_data = {}
                st.session_state.trader_file_names = []
                st.session_state.broker_file_names = []

                all_file_names = []

                # Process trader files
                for uploaded_file in trader_files:
                    df = recon_system.load_file_with_detection(uploaded_file)
                    if df is not None:
                        st.session_state.all_files_data[uploaded_file.name] = df
                        st.session_state.trader_file_names.append(uploaded_file.name)
                        all_file_names.append(uploaded_file.name)
                        st.success(f"✅ Loaded trader file: {uploaded_file.name} ({len(df)} records)")

                # Process broker files
                for uploaded_file in broker_files:
                    df = recon_system.load_file_with_detection(uploaded_file)
                    if df is not None:
                        st.session_state.all_files_data[uploaded_file.name] = df
                        st.session_state.broker_file_names.append(uploaded_file.name)
                        all_file_names.append(uploaded_file.name)
                        st.success(f"✅ Loaded broker file: {uploaded_file.name} ({len(df)} records)")

                # Check for suggested profiles
                if all_file_names:
                    suggested_profile = recon_system.get_suggested_profile(all_file_names)
                    if suggested_profile:
                        st.info(f"💡 **Smart Suggestion**: Found matching profile '{suggested_profile}' for your files!")
                        st.session_state.suggested_profile = suggested_profile
    
    # Main content area
    if st.session_state.all_files_data:
        st.success(f"📊 Loaded {len(st.session_state.all_files_data)} files successfully!")
        
        # Show file summary
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Trader Files", len(st.session_state.trader_file_names))
        with col2:
            st.metric("Broker Files", len(st.session_state.broker_file_names))
        with col3:
            total_records = sum(len(df) for df in st.session_state.all_files_data.values())
            st.metric("Total Records", total_records)
        
        # Collect all available columns
        all_columns = set()
        for df in st.session_state.all_files_data.values():
            all_columns.update(df.columns.tolist())
        all_columns = sorted(list(all_columns))
        
        st.markdown("---")
        st.header("🎯 Hybrid Column Mapping")
        st.markdown(f"**Available Columns**: {len(all_columns)} unique columns across all files")

        # Profile management section
        col1, col2, col3 = st.columns([2, 2, 1])

        with col1:
            # Load existing profile
            if recon_system.mapping_profiles:
                selected_profile = st.selectbox(
                    "🔄 Load Existing Profile",
                    [""] + list(recon_system.mapping_profiles.keys()),
                    help="Select a saved mapping profile to auto-populate fields"
                )

                if selected_profile and st.button("📥 Load Profile"):
                    mappings, logic = recon_system.apply_mapping_profile(selected_profile)
                    st.session_state.current_mappings = mappings
                    st.session_state.current_logic = logic
                    st.success(f"✅ Loaded profile: {selected_profile}")
                    st.rerun()

        with col2:
            # Apply suggested profile
            if 'suggested_profile' in st.session_state:
                if st.button(f"🎯 Apply Suggested: {st.session_state.suggested_profile}", type="secondary"):
                    mappings, logic = recon_system.apply_mapping_profile(st.session_state.suggested_profile)
                    st.session_state.current_mappings = mappings
                    st.session_state.current_logic = logic
                    st.success(f"✅ Applied suggested profile: {st.session_state.suggested_profile}")
                    st.rerun()

        # Column mapping interface
        mappings = st.session_state.current_mappings.copy()
        logic = st.session_state.current_logic.copy()

        # Create columns for better layout
        col1, col2 = st.columns([2, 1])
        
        with col1:
            st.subheader("Column Mappings")
            
            # Create mapping for each standard column (excluding calculated ones)
            for standard_col in recon_system.STANDARD_RECON_COLUMNS:
                if standard_col not in ["Contract Value", "Closing Contract Value", "Profit/Loss"]:
                    selected_col = st.selectbox(
                        f"**{standard_col}**",
                        [""] + all_columns,
                        key=f"mapping_{standard_col}",
                        help=f"Select source column for {standard_col}"
                    )
                    if selected_col:
                        mappings[standard_col] = selected_col
        
        with col2:
            st.subheader("Transformation Logic")
            
            # Account logic
            if "Account" in mappings:
                account_logic = st.selectbox(
                    "Account Extraction",
                    ["direct", "last_6_digits", "first_6_digits", "last_5_digits", "numeric_only", "alphanumeric_only"],
                    help="Choose how to extract account numbers"
                )
                logic["Account"] = account_logic
            
            # Auto-set logic for other fields
            logic["Date"] = "normalize"
            logic["State"] = "normalize"

        # Save current mappings to session state
        st.session_state.current_mappings = mappings
        st.session_state.current_logic = logic

        # Profile saving section
        if mappings:
            st.markdown("---")
            st.subheader("💾 Save Mapping Profile")

            col1, col2 = st.columns([3, 1])
            with col1:
                profile_name = st.text_input(
                    "Profile Name",
                    placeholder="e.g., 'Star AGL013 Profile', 'CQG Trader Profile'",
                    help="Give this mapping configuration a memorable name"
                )

            with col2:
                if st.button("💾 Save Profile", disabled=not profile_name):
                    # Extract file patterns for auto-suggestion
                    file_patterns = []
                    for file_name in st.session_state.all_files_data.keys():
                        # Extract meaningful parts of filename
                        base_name = file_name.replace('.csv', '').replace('.xlsx', '').replace('.xls', '')
                        if 'star' in base_name.lower():
                            file_patterns.append('star')
                        if 'cqg' in base_name.lower():
                            file_patterns.append('cqg')
                        if 'agl' in base_name.lower():
                            file_patterns.append('agl')
                        # Add more pattern detection as needed

                    recon_system.create_mapping_profile(
                        profile_name, mappings, logic, file_patterns
                    )
                    st.success(f"✅ Profile '{profile_name}' saved successfully!")
                    st.rerun()

        # Process reconciliation
        if st.button("⚡ Process Reconciliation", type="primary", disabled=not mappings):
            with st.spinner("Processing reconciliation..."):
                consolidated_df = recon_system.apply_reconciliation(
                    st.session_state.all_files_data, mappings, logic
                )
                
                st.session_state.consolidated_df = consolidated_df
                st.success(f"✅ Reconciliation completed! {len(consolidated_df)} records processed")
        
        # Display results
        if 'consolidated_df' in st.session_state:
            df = st.session_state.consolidated_df
            
            st.markdown("---")
            st.header("📊 Reconciliation Results")
            
            # Summary metrics
            col1, col2, col3, col4 = st.columns(4)
            with col1:
                st.metric("Total Records", len(df))
            with col2:
                total_contract_value = df['Contract Value'].sum()
                st.metric("Total Contract Value", f"₹{total_contract_value:,.2f}")
            with col3:
                total_commission = df['Commission'].sum()
                st.metric("Total Commission", f"₹{total_commission:,.2f}")
            with col4:
                total_pnl = df['Profit/Loss'].sum()
                st.metric("Net P&L", f"₹{total_pnl:,.2f}")
            
            # Tabs for different views
            tab1, tab2, tab3, tab4, tab5 = st.tabs(["📋 Console Data", "📈 Summary by Type", "📊 Summary by Source", "🎯 Mapping Analysis", "💾 Download"])
            
            with tab1:
                st.subheader("Consolidated Console Data")
                st.dataframe(df, use_container_width=True)
            
            with tab2:
                st.subheader("Summary by File Type")
                type_summary = df.groupby('File Type').agg({
                    'Date': 'count',
                    'Contract Value': 'sum',
                    'Commission': 'sum',
                    'Profit/Loss': 'sum'
                }).round(2)
                type_summary.columns = ['Record Count', 'Total Contract Value', 'Total Commission', 'Total P&L']
                st.dataframe(type_summary, use_container_width=True)
            
            with tab3:
                st.subheader("Summary by Input Source")
                source_summary = df.groupby('Input Source').agg({
                    'Date': 'count',
                    'Contract Value': 'sum',
                    'Commission': 'sum',
                    'Profit/Loss': 'sum'
                }).round(2)
                source_summary.columns = ['Record Count', 'Total Contract Value', 'Total Commission', 'Total P&L']
                st.dataframe(source_summary, use_container_width=True)
            
            with tab4:
                st.subheader("Mapping Analysis & Intelligence")

                # Show current mapping configuration
                st.write("**Current Mapping Configuration:**")
                mapping_df = pd.DataFrame([
                    {"Reconciliation Field": k, "Source Column": v, "Logic": logic.get(k, "direct")}
                    for k, v in mappings.items()
                ])
                st.dataframe(mapping_df, use_container_width=True)

                # Show transformation statistics
                st.write("**Data Transformation Results:**")
                col1, col2 = st.columns(2)

                with col1:
                    # Date normalization stats
                    if 'Date' in df.columns:
                        date_success = df['Date'].notna().sum()
                        date_total = len(df)
                        st.metric("Date Normalization Success", f"{date_success}/{date_total}",
                                f"{(date_success/date_total*100):.1f}%")

                with col2:
                    # State normalization stats
                    if 'State' in df.columns:
                        state_success = df['State'].notna().sum()
                        state_total = len(df)
                        st.metric("State Normalization Success", f"{state_success}/{state_total}",
                                f"{(state_success/state_total*100):.1f}%")

                # Show file type detection results
                st.write("**File Type Detection:**")
                file_type_detection = df.groupby(['Input Source', 'File Type']).size().reset_index(name='Records')
                st.dataframe(file_type_detection, use_container_width=True)

                # Show business logic calculation results
                st.write("**Business Logic Calculations:**")
                calc_stats = {
                    "Contract Values Calculated": (df['Contract Value'] > 0).sum(),
                    "P&L Calculations": (df['Profit/Loss'] != 0).sum(),
                    "Commission Applied": (df['Commission'] > 0).sum()
                }

                for metric, value in calc_stats.items():
                    st.metric(metric, f"{value}/{len(df)}", f"{(value/len(df)*100):.1f}%")

            with tab5:
                st.subheader("Download Results")
                
                # Create Excel file in memory
                output = BytesIO()
                with pd.ExcelWriter(output, engine='openpyxl') as writer:
                    df.to_excel(writer, sheet_name='Console', index=False)
                    type_summary.to_excel(writer, sheet_name='Summary_by_Type')
                    source_summary.to_excel(writer, sheet_name='Summary_by_Source')
                
                output.seek(0)
                
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"Dynamic_Console_Reconciliation_{timestamp}.xlsx"
                
                st.download_button(
                    label="📥 Download Excel Report",
                    data=output.getvalue(),
                    file_name=filename,
                    mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                )
    
    else:
        st.info("👆 Please upload trader and broker files using the sidebar to get started.")
        
        # Show comprehensive instructions
        with st.expander("📖 Hybrid System Guide & Best Practices"):
            st.markdown("""
            ## 🎯 The Hybrid Approach Explained

            This system solves the **real-world reconciliation challenge** by combining:

            ### 1. 🗺️ User-Driven Mapping (Handles Column Variability)
            **Problem**: Date could be "Trade Date", "Expiry Date", "Transaction Date", etc.
            **Solution**: You map once, system remembers forever.

            - Upload files from different brokers/traders
            - Map their unique column names to standard reconciliation fields
            - Save as reusable profiles (e.g., "Star AGL013 Profile", "CQG Trader Profile")

            ### 2. 🧠 Intelligent Backend Logic (Handles Data Transformation)
            **Problem**: Complex transformations like "MH → Maharashtra", "last 6 digits of account"
            **Solution**: Pre-built, configurable transformation engine.

            - **Date Normalization**: 15+ formats → YYYY-MM-DD automatically
            - **State Standardization**: MH/mah/maha → Maharashtra (25+ mappings)
            - **Account Extraction**: Choose pattern (last 6 digits, first 6 digits, etc.)
            - **Business Calculations**: Contract Value, P&L calculations applied automatically

            ### 3. 🔄 Reusable Profiles (Enables Scale & Automation)
            **Problem**: Don't want to remap every time you get files from the same broker
            **Solution**: Smart profile system with auto-suggestions.

            - System learns file patterns and suggests profiles
            - One-click application for recurring reconciliations
            - Profile management with usage analytics

            ## 🚀 Workflow for Large-Scale Operations

            ### Initial Setup (Per Broker/Trader Type):
            1. **Upload Sample Files**: Upload 1-2 files from each broker/trader
            2. **Create Mappings**: Map columns once (Trade Date → Date, UCC Code → Account, etc.)
            3. **Configure Logic**: Choose transformation rules (last 6 digits for accounts, etc.)
            4. **Save Profile**: Give it a memorable name ("Star AGL013 Profile")

            ### Daily Operations (One-Click Automation):
            1. **Upload Files**: Drop 10+ trader files, 20+ broker files
            2. **Auto-Suggest**: System suggests profiles based on file names
            3. **One-Click Apply**: Apply saved profile with single button
            4. **Process & Download**: Get consolidated Excel with all calculations

            ## 📊 Supported Scenarios

            ### File Formats
            - **CSV files** (.csv) with automatic header detection
            - **Excel files** (.xlsx, .xls) with sheet processing
            - **Mixed formats** in single reconciliation run

            ### Business Logic
            - **Contract Value** = Price × Lot × Contract × Multiplication Factor
            - **Closing Contract Value** = Closing Price × Lot × Contract × Multiplication Factor
            - **Profit/Loss** = Closing Contract Value - Contract Value - Commission

            ### Data Transformations
            - **Date Formats**: DD/MM/YYYY, MM-DD-YYYY, YYYY-MM-DD, DD-MMM-YYYY, etc.
            - **State Variations**: MH/mh/maha → Maharashtra, gj/gujarat → Gujarat, etc.
            - **Account Patterns**: Direct, last 6 digits, first 6 digits, numeric only, etc.

            ## 🎯 Perfect For
            - **Trading Firms**: Multiple brokers with different file formats
            - **Daily Reconciliation**: Large volumes of recurring files
            - **Audit Requirements**: Complete traceability and standardization
            - **Team Operations**: Shareable profiles and consistent processes
            """)

    else:
        st.info("👆 Please upload trader and broker files using the sidebar to get started.")

        # Quick start section
        st.markdown("### 🚀 Quick Start")
        col1, col2 = st.columns(2)

        with col1:
            st.markdown("""
            **First Time Setup:**
            1. Upload sample files from each broker/trader
            2. Map columns to reconciliation fields
            3. Save as reusable profiles
            """)

        with col2:
            st.markdown("""
            **Daily Operations:**
            1. Upload all files (10+ trader, 20+ broker)
            2. Apply saved profiles (one-click)
            3. Download consolidated Excel report
            """)

if __name__ == "__main__":
    main()
