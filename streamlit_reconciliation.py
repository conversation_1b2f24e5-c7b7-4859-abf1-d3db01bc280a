#!/usr/bin/env python3
"""
Streamlit Dynamic Reconciliation System for Traders and Brokers
Web-based interface for handling 10+ trader files and 20+ broker files
"""

import streamlit as st
import pandas as pd
import numpy as np
from datetime import datetime
import os
import hashlib
import json
from io import BytesIO
import logging
from typing import Dict, List, Any, Optional

# Configure page
st.set_page_config(
    page_title="Dynamic Reconciliation System",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class StreamlitReconciliationSystem:
    def __init__(self):
        # Enhanced standard columns based on requirements
        self.STANDARD_RECON_COLUMNS = [
            "Date", "Account", "State", "Contract", "Expiry", "Price", "Lot",
            "Multiplication Factor", "Contract Value", "Commission", "Input Source",
            "Venue", "Closing Price", "Closing Contract Value", "Profit/Loss",
            "Trade Type", "Quantity", "Trader Name", "Order ID", "Broker Code"
        ]
        
        # State mapping for normalization
        self.STATE_MAPPINGS = {
            'mh': 'Maharashtra', 'maharashtra': 'Maharashtra', 'maha': 'Maharashtra',
            'dl': 'Delhi', 'delhi': 'Delhi', 'new delhi': 'Delhi',
            'gj': 'Gujarat', 'gujarat': 'Gujarat', 'guj': 'Gujarat',
            'ka': 'Karnataka', 'karnataka': 'Karnataka', 'kar': 'Karnataka',
            'tn': 'Tamil Nadu', 'tamil nadu': 'Tamil Nadu', 'tamilnadu': 'Tamil Nadu',
            'ap': 'Andhra Pradesh', 'andhra pradesh': 'Andhra Pradesh',
            'ts': 'Telangana', 'telangana': 'Telangana',
            'wb': 'West Bengal', 'west bengal': 'West Bengal',
            'up': 'Uttar Pradesh', 'uttar pradesh': 'Uttar Pradesh',
            'rj': 'Rajasthan', 'rajasthan': 'Rajasthan',
            'mp': 'Madhya Pradesh', 'madhya pradesh': 'Madhya Pradesh',
            'or': 'Odisha', 'odisha': 'Odisha', 'orissa': 'Odisha',
            'pb': 'Punjab', 'punjab': 'Punjab',
            'hr': 'Haryana', 'haryana': 'Haryana',
            'jh': 'Jharkhand', 'jharkhand': 'Jharkhand',
            'br': 'Bihar', 'bihar': 'Bihar',
            'as': 'Assam', 'assam': 'Assam',
            'kl': 'Kerala', 'kerala': 'Kerala',
            'ch': 'Chandigarh', 'chandigarh': 'Chandigarh'
        }
        
        # Account extraction patterns
        self.ACCOUNT_PATTERNS = {
            'direct': lambda x: str(x).strip(),
            'last_6_digits': lambda x: str(x)[-6:] if len(str(x)) >= 6 else str(x),
            'first_6_digits': lambda x: str(x)[:6] if len(str(x)) >= 6 else str(x),
            'last_5_digits': lambda x: str(x)[-5:] if len(str(x)) >= 5 else str(x),
            'numeric_only': lambda x: ''.join(filter(str.isdigit, str(x))),
            'alphanumeric_only': lambda x: ''.join(filter(str.isalnum, str(x)))
        }
    
    def normalize_date(self, date_str: Any) -> Optional[str]:
        """Enhanced date normalization with multiple formats"""
        if pd.isna(date_str) or date_str == '':
            return None
        
        try:
            if isinstance(date_str, datetime):
                return date_str.strftime('%Y-%m-%d')
            
            date_str = str(date_str).strip()
            
            # Common date formats
            date_formats = [
                '%Y-%m-%d', '%d/%m/%Y', '%m/%d/%Y', '%d-%m-%Y', '%m-%d-%Y',
                '%d/%m/%y', '%m/%d/%y', '%d-%m-%y', '%m-%d-%y',
                '%d-%b-%Y', '%d-%B-%Y', '%b-%d-%Y', '%B-%d-%Y',
                '%d %b %Y', '%d %B %Y', '%b %d %Y', '%B %d %Y',
                '%Y/%m/%d', '%d.%m.%Y', '%m.%d.%Y'
            ]
            
            for fmt in date_formats:
                try:
                    return datetime.strptime(date_str, fmt).strftime('%Y-%m-%d')
                except ValueError:
                    continue
            
            # Try pandas parsing as fallback
            return pd.to_datetime(date_str, dayfirst=True).strftime('%Y-%m-%d')
            
        except Exception as e:
            logger.warning(f"Could not normalize date '{date_str}': {e}")
            return None
    
    def normalize_state(self, state_str: Any) -> Optional[str]:
        """Normalize state names using mapping"""
        if pd.isna(state_str) or state_str == '':
            return None
        
        state_clean = str(state_str).lower().strip()
        return self.STATE_MAPPINGS.get(state_clean, str(state_str).title())
    
    def extract_account_number(self, account_value: Any, logic_type: str = "direct") -> Optional[str]:
        """Enhanced account number extraction with multiple patterns"""
        if pd.isna(account_value) or account_value == '':
            return None
        
        if logic_type in self.ACCOUNT_PATTERNS:
            return self.ACCOUNT_PATTERNS[logic_type](account_value)
        else:
            return str(account_value).strip()
    
    def calculate_contract_value(self, price: Any, lot: Any, contract: Any, mult_factor: Any) -> float:
        """Calculate Contract Value = Price × Lot × Contract × Multiplication Factor"""
        try:
            price = float(price) if pd.notna(price) and price != '' else 0
            lot = float(lot) if pd.notna(lot) and lot != '' else 1
            contract = float(contract) if pd.notna(contract) and contract != '' else 1
            mult_factor = float(mult_factor) if pd.notna(mult_factor) and mult_factor != '' else 1
            
            return price * lot * contract * mult_factor
        except (ValueError, TypeError):
            return 0.0
    
    def calculate_closing_contract_value(self, closing_price: Any, lot: Any, contract: Any, mult_factor: Any) -> float:
        """Calculate Closing Contract Value"""
        return self.calculate_contract_value(closing_price, lot, contract, mult_factor)
    
    def calculate_profit_loss(self, contract_value: Any, closing_contract_value: Any, commission: Any = 0) -> float:
        """Calculate Profit/Loss = Closing Contract Value - Contract Value - Commission"""
        try:
            cv = float(contract_value) if pd.notna(contract_value) else 0
            ccv = float(closing_contract_value) if pd.notna(closing_contract_value) else 0
            comm = float(commission) if pd.notna(commission) else 0
            
            return ccv - cv - comm
        except (ValueError, TypeError):
            return 0.0
    
    def load_file_with_detection(self, uploaded_file) -> Optional[pd.DataFrame]:
        """Load uploaded file with automatic header detection"""
        try:
            # Try CSV first
            if uploaded_file.name.endswith('.csv'):
                # Try different skip rows to find the header
                for skip_rows in range(0, 10):
                    try:
                        uploaded_file.seek(0)  # Reset file pointer
                        df = pd.read_csv(uploaded_file, skiprows=skip_rows)
                        if len(df.columns) > 3 and not df.empty:
                            return df
                    except:
                        continue
            
            # Try Excel format
            elif uploaded_file.name.endswith(('.xlsx', '.xls')):
                uploaded_file.seek(0)
                df = pd.read_excel(uploaded_file)
                return df
            
            return None
            
        except Exception as e:
            st.error(f"Error loading file {uploaded_file.name}: {e}")
            return None
    
    def apply_reconciliation(self, all_files_data: Dict[str, pd.DataFrame], 
                           mappings: Dict[str, str], logic: Dict[str, str]) -> pd.DataFrame:
        """Apply reconciliation with dynamic mappings and business logic"""
        consolidated_records = []
        
        for file_name, df in all_files_data.items():
            # Determine file type based on session state
            file_type = "Trader" if file_name in st.session_state.get('trader_file_names', []) else "Broker"
            
            for _, row in df.iterrows():
                record = {}
                
                # Apply mappings with transformations
                for standard_col, source_col in mappings.items():
                    if source_col and source_col in df.columns:
                        raw_value = row[source_col]
                        
                        # Apply transformation logic
                        if standard_col == "Date":
                            record[standard_col] = self.normalize_date(raw_value)
                        elif standard_col == "State":
                            record[standard_col] = self.normalize_state(raw_value)
                        elif standard_col == "Account":
                            account_logic = logic.get(standard_col, "direct")
                            record[standard_col] = self.extract_account_number(raw_value, account_logic)
                        else:
                            # Direct copy with basic cleaning
                            if pd.notna(raw_value) and raw_value != '':
                                record[standard_col] = str(raw_value).strip()
                            else:
                                record[standard_col] = None
                    else:
                        record[standard_col] = None
                
                # Add metadata
                record['Input Source'] = file_name
                record['File Type'] = file_type
                
                # Calculate business logic fields
                record['Contract Value'] = self.calculate_contract_value(
                    record.get('Price'), record.get('Lot'), 
                    record.get('Contract'), record.get('Multiplication Factor')
                )
                
                record['Closing Contract Value'] = self.calculate_closing_contract_value(
                    record.get('Closing Price'), record.get('Lot'), 
                    record.get('Contract'), record.get('Multiplication Factor')
                )
                
                record['Profit/Loss'] = self.calculate_profit_loss(
                    record['Contract Value'], record['Closing Contract Value'],
                    record.get('Commission', 0)
                )
                
                consolidated_records.append(record)
        
        # Create consolidated DataFrame
        consolidated_df = pd.DataFrame(consolidated_records)
        
        # Ensure all standard columns exist
        for col in self.STANDARD_RECON_COLUMNS:
            if col not in consolidated_df.columns:
                consolidated_df[col] = None
        
        # Reorder columns
        consolidated_df = consolidated_df[self.STANDARD_RECON_COLUMNS + ['Input Source', 'File Type']]
        
        return consolidated_df

# Initialize the system
@st.cache_resource
def get_recon_system():
    return StreamlitReconciliationSystem()

def main():
    # Initialize system
    recon_system = get_recon_system()
    
    # App header
    st.title("🚀 Dynamic Reconciliation System")
    st.markdown("### Brokers vs Traders Reconciliation with 10+ Trader & 20+ Broker Files")
    
    # Initialize session state
    if 'all_files_data' not in st.session_state:
        st.session_state.all_files_data = {}
    if 'trader_file_names' not in st.session_state:
        st.session_state.trader_file_names = []
    if 'broker_file_names' not in st.session_state:
        st.session_state.broker_file_names = []
    
    # Sidebar for file uploads
    with st.sidebar:
        st.header("📁 File Upload")
        
        # Trader files upload
        st.subheader("🔵 Trader Files")
        trader_files = st.file_uploader(
            "Upload Trader Files (10+ supported)",
            type=['csv', 'xlsx', 'xls'],
            accept_multiple_files=True,
            key="trader_files"
        )
        
        # Broker files upload
        st.subheader("🔴 Broker Files") 
        broker_files = st.file_uploader(
            "Upload Broker Files (20+ supported)",
            type=['csv', 'xlsx', 'xls'],
            accept_multiple_files=True,
            key="broker_files"
        )
        
        # Process uploaded files
        if st.button("🔄 Load Files", type="primary"):
            st.session_state.all_files_data = {}
            st.session_state.trader_file_names = []
            st.session_state.broker_file_names = []
            
            # Process trader files
            for uploaded_file in trader_files:
                df = recon_system.load_file_with_detection(uploaded_file)
                if df is not None:
                    st.session_state.all_files_data[uploaded_file.name] = df
                    st.session_state.trader_file_names.append(uploaded_file.name)
                    st.success(f"✅ Loaded trader file: {uploaded_file.name} ({len(df)} records)")
            
            # Process broker files
            for uploaded_file in broker_files:
                df = recon_system.load_file_with_detection(uploaded_file)
                if df is not None:
                    st.session_state.all_files_data[uploaded_file.name] = df
                    st.session_state.broker_file_names.append(uploaded_file.name)
                    st.success(f"✅ Loaded broker file: {uploaded_file.name} ({len(df)} records)")
    
    # Main content area
    if st.session_state.all_files_data:
        st.success(f"📊 Loaded {len(st.session_state.all_files_data)} files successfully!")
        
        # Show file summary
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Trader Files", len(st.session_state.trader_file_names))
        with col2:
            st.metric("Broker Files", len(st.session_state.broker_file_names))
        with col3:
            total_records = sum(len(df) for df in st.session_state.all_files_data.values())
            st.metric("Total Records", total_records)
        
        # Collect all available columns
        all_columns = set()
        for df in st.session_state.all_files_data.values():
            all_columns.update(df.columns.tolist())
        all_columns = sorted(list(all_columns))
        
        st.markdown("---")
        st.header("🎯 Dynamic Column Mapping")
        st.markdown(f"**Available Columns**: {len(all_columns)} unique columns across all files")
        
        # Column mapping interface
        mappings = {}
        logic = {}
        
        # Create columns for better layout
        col1, col2 = st.columns([2, 1])
        
        with col1:
            st.subheader("Column Mappings")
            
            # Create mapping for each standard column (excluding calculated ones)
            for standard_col in recon_system.STANDARD_RECON_COLUMNS:
                if standard_col not in ["Contract Value", "Closing Contract Value", "Profit/Loss"]:
                    selected_col = st.selectbox(
                        f"**{standard_col}**",
                        [""] + all_columns,
                        key=f"mapping_{standard_col}",
                        help=f"Select source column for {standard_col}"
                    )
                    if selected_col:
                        mappings[standard_col] = selected_col
        
        with col2:
            st.subheader("Transformation Logic")
            
            # Account logic
            if "Account" in mappings:
                account_logic = st.selectbox(
                    "Account Extraction",
                    ["direct", "last_6_digits", "first_6_digits", "last_5_digits", "numeric_only", "alphanumeric_only"],
                    help="Choose how to extract account numbers"
                )
                logic["Account"] = account_logic
            
            # Auto-set logic for other fields
            logic["Date"] = "normalize"
            logic["State"] = "normalize"
        
        # Process reconciliation
        if st.button("⚡ Process Reconciliation", type="primary", disabled=not mappings):
            with st.spinner("Processing reconciliation..."):
                consolidated_df = recon_system.apply_reconciliation(
                    st.session_state.all_files_data, mappings, logic
                )
                
                st.session_state.consolidated_df = consolidated_df
                st.success(f"✅ Reconciliation completed! {len(consolidated_df)} records processed")
        
        # Display results
        if 'consolidated_df' in st.session_state:
            df = st.session_state.consolidated_df
            
            st.markdown("---")
            st.header("📊 Reconciliation Results")
            
            # Summary metrics
            col1, col2, col3, col4 = st.columns(4)
            with col1:
                st.metric("Total Records", len(df))
            with col2:
                total_contract_value = df['Contract Value'].sum()
                st.metric("Total Contract Value", f"₹{total_contract_value:,.2f}")
            with col3:
                total_commission = df['Commission'].sum()
                st.metric("Total Commission", f"₹{total_commission:,.2f}")
            with col4:
                total_pnl = df['Profit/Loss'].sum()
                st.metric("Net P&L", f"₹{total_pnl:,.2f}")
            
            # Tabs for different views
            tab1, tab2, tab3, tab4 = st.tabs(["📋 Console Data", "📈 Summary by Type", "📊 Summary by Source", "💾 Download"])
            
            with tab1:
                st.subheader("Consolidated Console Data")
                st.dataframe(df, use_container_width=True)
            
            with tab2:
                st.subheader("Summary by File Type")
                type_summary = df.groupby('File Type').agg({
                    'Date': 'count',
                    'Contract Value': 'sum',
                    'Commission': 'sum',
                    'Profit/Loss': 'sum'
                }).round(2)
                type_summary.columns = ['Record Count', 'Total Contract Value', 'Total Commission', 'Total P&L']
                st.dataframe(type_summary, use_container_width=True)
            
            with tab3:
                st.subheader("Summary by Input Source")
                source_summary = df.groupby('Input Source').agg({
                    'Date': 'count',
                    'Contract Value': 'sum',
                    'Commission': 'sum',
                    'Profit/Loss': 'sum'
                }).round(2)
                source_summary.columns = ['Record Count', 'Total Contract Value', 'Total Commission', 'Total P&L']
                st.dataframe(source_summary, use_container_width=True)
            
            with tab4:
                st.subheader("Download Results")
                
                # Create Excel file in memory
                output = BytesIO()
                with pd.ExcelWriter(output, engine='openpyxl') as writer:
                    df.to_excel(writer, sheet_name='Console', index=False)
                    type_summary.to_excel(writer, sheet_name='Summary_by_Type')
                    source_summary.to_excel(writer, sheet_name='Summary_by_Source')
                
                output.seek(0)
                
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"Dynamic_Console_Reconciliation_{timestamp}.xlsx"
                
                st.download_button(
                    label="📥 Download Excel Report",
                    data=output.getvalue(),
                    file_name=filename,
                    mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                )
    
    else:
        st.info("👆 Please upload trader and broker files using the sidebar to get started.")
        
        # Show sample data structure
        with st.expander("📖 Sample Data Structure & Instructions"):
            st.markdown("""
            ### 🎯 How to Use This System
            
            1. **Upload Files**: Use the sidebar to upload trader and broker files separately
            2. **Column Mapping**: Map each reconciliation field to source columns from your files
            3. **Transformation Logic**: Choose how to process Account numbers and other fields
            4. **Process**: Click "Process Reconciliation" to apply business logic
            5. **Download**: Get your consolidated Excel report with summaries
            
            ### 📊 Supported File Formats
            - **CSV files** (.csv)
            - **Excel files** (.xlsx, .xls)
            - **Multiple files** (10+ trader, 20+ broker files supported)
            
            ### 🧮 Business Logic Applied
            - **Contract Value** = Price × Lot × Contract × Multiplication Factor
            - **Closing Contract Value** = Closing Price × Lot × Contract × Multiplication Factor
            - **Profit/Loss** = Closing Contract Value - Contract Value - Commission
            
            ### 🔄 Data Transformations
            - **Date Normalization**: All date formats → YYYY-MM-DD
            - **State Standardization**: MH/maha → Maharashtra, gj → Gujarat, etc.
            - **Account Extraction**: Last 6 digits, first 6 digits, numeric only, etc.
            """)

if __name__ == "__main__":
    main()
