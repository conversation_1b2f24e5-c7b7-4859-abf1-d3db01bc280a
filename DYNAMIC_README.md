# Dynamic Reconciliation System

## 🚀 Ultra-Dynamic Brokers vs Traders Reconciliation

This system handles **10+ trader files and 20+ broker files** with complete flexibility in column mapping and business logic transformations.

## ✨ Key Features

### 🔄 Dynamic File Processing
- **Separate Upload Interface**: Upload trader and broker files independently
- **Multi-Format Support**: CSV, Excel files with automatic header detection
- **Scalable**: Handles 10+ trader files and 20+ broker files simultaneously
- **Duplicate Prevention**: Automatic file tracking to prevent reprocessing

### 🎯 Intelligent Column Mapping
- **Universal Column Detection**: Extracts all column names from all uploaded files
- **Dropdown Interface**: Select source columns from unified dropdown for each reconciliation field
- **Smart Suggestions**: System learns from previous mappings
- **Flexible Mapping**: Different files can map to different source columns

### 🧮 Advanced Business Logic

#### Date Normalization
- **Multi-Format Support**: Handles 15+ date formats automatically
- **Automatic Detection**: `DD/MM/YYYY`, `MM-DD-YYYY`, `YYYY-MM-DD`, etc.
- **Consistent Output**: All dates normalized to `YYYY-MM-DD` format

#### Account Number Extraction
- **Multiple Patterns**:
  - `direct`: Use as-is
  - `last_6_digits`: Extract last 6 digits
  - `first_6_digits`: Extract first 6 digits
  - `last_5_digits`: Extract last 5 digits
  - `numeric_only`: Extract only numbers
  - `alphanumeric_only`: Extract letters and numbers only

#### State Standardization
- **Comprehensive Mapping**: 25+ state abbreviations and variations
- **Examples**: `MH/mh/maha` → `Maharashtra`, `gj/gujarat` → `Gujarat`
- **Auto-Normalization**: Handles case variations and common abbreviations

### 📊 Business Calculations

#### Contract Value Calculation
```
Contract Value = Price × Lot × Contract × Multiplication Factor
```

#### Closing Contract Value
```
Closing Contract Value = Closing Price × Lot × Contract × Multiplication Factor
```

#### Profit/Loss Calculation
```
Profit/Loss = Closing Contract Value - Contract Value - Commission
```

## 🏗️ System Architecture

### Standard Reconciliation Columns
- **Date** (with normalization)
- **Account** (with extraction logic)
- **State** (with standardization)
- **Contract**
- **Expiry**
- **Price**
- **Lot**
- **Multiplication Factor**
- **Contract Value** (calculated)
- **Commission**
- **Input Source** (auto-added)
- **Venue**
- **Closing Price**
- **Closing Contract Value** (calculated)
- **Profit/Loss** (calculated)
- **Trade Type**
- **Quantity**
- **Trader Name**
- **Order ID**
- **Broker Code**

## 🚀 Quick Start

### 1. Create Sample Data
```bash
python test_dynamic_system.py
```
This creates 5 sample files (3 trader + 2 broker) with different formats.

### 2. Run Dynamic Reconciliation
```bash
python dynamic_reconciliation_system.py
```

### 3. Follow the GUI Workflow

#### Step 1: File Upload
- **Trader Files Section**: Add multiple trader files
- **Broker Files Section**: Add multiple broker files
- **Support**: CSV and Excel files

#### Step 2: Dynamic Column Mapping
- **Universal Dropdown**: All columns from all files available
- **Transformation Logic**: Select appropriate logic for each field
- **Account Logic**: Choose extraction pattern (last 6 digits, etc.)
- **Auto-Normalization**: Date and State fields auto-normalized

#### Step 3: Processing & Results
- **Real-time Processing**: See progress for each file
- **Business Logic**: Automatic calculations applied
- **Console Sheet**: Generated with all reconciled data
- **Summary Sheets**: By file type and source

## 📁 Output Files

### Console Sheet
- **Main Data**: All reconciled records with calculated fields
- **Standardized Format**: Consistent columns across all sources
- **Metadata**: Input source and file type tracking

### Summary Sheets
- **By File Type**: Trader vs Broker summaries
- **By Source**: Individual file summaries
- **Key Metrics**: Record counts, contract values, P&L totals

## 🔧 Advanced Features

### File Tracking
- **Hash-based Detection**: Prevents duplicate processing
- **Persistent Storage**: Remembers processed files across sessions
- **Smart Updates**: Only processes new or changed files

### Mapping Memory
- **Saved Mappings**: System remembers your column mappings
- **Quick Setup**: Reuse mappings for similar file structures
- **Flexible Override**: Easy to modify mappings when needed

### Error Handling
- **Robust Processing**: Continues processing even if some files fail
- **Detailed Logging**: Complete audit trail of all operations
- **User Feedback**: Clear status messages and error reporting

## 📊 Sample Data Structure

### Input Files (Various Formats)
```
Trader Files:
- Date, Account, State, Symbol, Side, Quantity, Price...
- Trade Date, UCC Code, Client State, Instrument...
- Transaction Date, Account Number, Location...

Broker Files:
- Date, Client Account, State, Symbol, Side...
- Trading Date, Account ID, Region, Instrument Name...
```

### Output Console Sheet
```
Date | Account | State | Contract | Price | Lot | Contract Value | P&L | Input Source
2024-04-24 | 001234 | Maharashtra | NIFTY24APR19000CE | 125.50 | 50 | 6275.00 | 237.50 | Trader_CQG_File_1.csv
```

## 🎯 Use Cases

### Large Scale Operations
- **Multiple Trading Desks**: Each with different file formats
- **Various Brokers**: Different reporting structures
- **Daily Reconciliation**: Process 30+ files daily
- **Audit Requirements**: Complete traceability and calculations

### Flexible Environments
- **Changing Formats**: Adapt to new file structures easily
- **Multiple Sources**: Combine data from various systems
- **Custom Logic**: Apply specific business rules per field
- **Scalable Processing**: Handle growing data volumes

## 🔍 Technical Requirements

### Dependencies
```
pandas>=1.5.0
openpyxl>=3.0.0
numpy>=1.21.0
tkinter (included with Python)
```

### System Requirements
- **Python 3.8+**
- **Memory**: 4GB+ recommended for large datasets
- **Storage**: Sufficient space for output files
- **Display**: GUI requires desktop environment

## 🎉 Success Metrics

After running the system, you'll see:
- **Total Records Processed**: Count across all files
- **Total Contract Value**: Sum of all contract values
- **Net P&L**: Total profit/loss across all trades
- **File Breakdown**: Records per trader/broker file
- **Processing Time**: Complete audit of system performance

This dynamic system eliminates the need for hardcoded mappings and provides complete flexibility for handling diverse file formats in financial reconciliation workflows.
