#!/usr/bin/env python3
"""
Command-line test for the Enhanced Reconciliation System
Tests the core functionality without GUI
"""

import pandas as pd
import os
from enhanced_reconciliation_system import ReconciliationSystem

def test_reconciliation_cli():
    """Test reconciliation system with predefined mappings"""
    print("🧪 Testing Reconciliation System (CLI Mode)")
    print("=" * 60)
    
    # Initialize system
    recon_system = ReconciliationSystem()
    
    # Sample files to process
    sample_files = [
        "Sample_Star_AGL013_Trade_File.csv",
        "Sample_CQG_Trader_File.csv", 
        "Sample_Star07_Trade_File.csv"
    ]
    
    # Check if files exist
    existing_files = []
    for file in sample_files:
        if os.path.exists(file):
            existing_files.append(file)
            print(f"✅ Found: {file}")
        else:
            print(f"❌ Missing: {file}")
    
    if not existing_files:
        print("❌ No sample files found. Run test_reconciliation.py first.")
        return
    
    # Load files
    all_files_data = {}
    for file_path in existing_files:
        df, skip_rows = recon_system.load_file_with_detection(file_path)
        if df is not None:
            file_name = os.path.basename(file_path)
            all_files_data[file_name] = df
            print(f"📊 Loaded {file_name}: {len(df)} rows, {len(df.columns)} columns")
    
    # Define predefined mappings for testing
    predefined_mappings = {
        "Sample_Star_AGL013_Trade_File.csv": {
            "Date": "Trade Date",
            "Account Number": "UCC Code", 
            "State": "State",
            "Instrument/Symbol": "Symbol",
            "Trade Type": "Buy/Sell",
            "Quantity/Size": "Qty",
            "Price": "Price",
            "Value/Notional": "Traded Value",
            "Broker ID/Code": "Exchange",
            "Trader Name/ID": "Client Name",
            "Order ID/Trade ID": "Order No.",
            "Lot": "Lot",
            "Contract": "Contract",
            "Multiplication Factor": "Multiplication Factor",
            "Commission": "Commission"
        },
        "Sample_CQG_Trader_File.csv": {
            "Date": "Date",
            "Account Number": "Account",
            "Instrument/Symbol": "Symbol", 
            "Trade Type": "Side",
            "Quantity/Size": "Quantity",
            "Price": "Price",
            "Value/Notional": "Value",
            "Trader Name/ID": "Trader",
            "Order ID/Trade ID": "Order ID",
            "Commission": "Commission",
            "Broker ID/Code": "Exchange"
        },
        "Sample_Star07_Trade_File.csv": {
            "Date": "Transaction Date",
            "Account Number": "Client Code",
            "State": "State Code", 
            "Instrument/Symbol": "Instrument",
            "Trade Type": "Transaction Type",
            "Quantity/Size": "Volume",
            "Price": "Rate",
            "Value/Notional": "Total Value",
            "Broker ID/Code": "Market",
            "Trader Name/ID": "Trader ID",
            "Order ID/Trade ID": "Reference",
            "Lot": "Lot Size",
            "Multiplication Factor": "Multiplier",
            "Commission": "Brokerage"
        }
    }
    
    print("\n🔗 Applying predefined column mappings...")
    
    # Apply reconciliation
    consolidated_df = recon_system.apply_reconciliation_with_calculations(
        all_files_data, predefined_mappings
    )
    
    if consolidated_df is not None and not consolidated_df.empty:
        print(f"✅ Reconciliation completed: {len(consolidated_df)} total records")
        
        # Show sample results
        print("\n📋 Sample Results (first 3 rows):")
        print("-" * 80)
        
        # Display key columns
        key_columns = [
            "Date", "Account Number", "Instrument/Symbol", "Trade Type", 
            "Quantity/Size", "Price", "Contract Value", "Input Source"
        ]
        
        available_columns = [col for col in key_columns if col in consolidated_df.columns]
        sample_df = consolidated_df[available_columns].head(3)
        
        for i, row in sample_df.iterrows():
            print(f"\nRecord {i+1}:")
            for col in available_columns:
                value = row[col]
                if pd.notna(value):
                    print(f"  {col}: {value}")
        
        # Show summary by source
        print(f"\n📊 Summary by Input Source:")
        source_summary = consolidated_df.groupby('Input Source').agg({
            'Date': 'count',
            'Contract Value': 'sum',
            'Commission': 'sum'
        }).round(2)
        source_summary.columns = ['Record Count', 'Total Contract Value', 'Total Commission']
        print(source_summary)
        
        # Save results
        output_file = "Test_Console_Reconciliation.xlsx"
        success = recon_system.save_console_sheet(consolidated_df, output_file)
        
        if success:
            print(f"\n💾 Results saved to: {output_file}")
        else:
            print(f"\n❌ Failed to save results")
        
        # Show calculated fields
        print(f"\n🧮 Calculation Results:")
        calc_summary = consolidated_df.agg({
            'Contract Value': ['count', 'sum', 'mean'],
            'Commission': ['sum', 'mean']
        }).round(2)
        print(calc_summary)
        
    else:
        print("❌ Reconciliation failed")

if __name__ == "__main__":
    test_reconciliation_cli()
